@extends('layouts.contentNavbarLayout')

@section('title', 'Laporan <PERSON>')

@section('content')
<div class="container py-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card bg-gradient-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h3 class="card-title mb-1">📊 Laporan Keuangan Komprehensif</h3>
              <p class="mb-0">Periode: Tahun {{ $tahun }} | Diperbarui: {{ now()->format('d M Y, H:i') }} WIB</p>
            </div>
            <div class="text-end">
              <div class="d-flex gap-2">
                <button class="btn btn-light btn-sm" onclick="window.print()">
                  <i class="bx bx-printer me-1"></i>Cetak
                </button>
                <button class="btn btn-light btn-sm" onclick="exportToExcel()">
                  <i class="bx bx-download me-1"></i>Export
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" class="row align-items-end">
            <div class="col-md-3">
              <label class="form-label">Pilih Tahun</label>
              <select name="tahun" class="form-select" onchange="this.form.submit()">
                @for ($i = now()->year; $i >= 2020; $i--)
                <option value="{{ $i }}" {{ $i == $tahun ? 'selected' : '' }}>{{ $i }}</option>
                @endfor
              </select>
            </div>
            <div class="col-md-9">
              <div class="row">
                <div class="col-md-4">
                  <small class="text-muted">Status Sistem:</small>
                  <div class="d-flex align-items-center">
                    <span class="badge bg-success me-2">Online</span>
                    <small>Data real-time</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <small class="text-muted">Total Transaksi:</small>
                  <div><strong>{{ number_format($totalTransaksi ?? 0) }}</strong> transaksi</div>
                </div>
                <div class="col-md-4">
                  <small class="text-muted">Periode Analisis:</small>
                  <div><strong>12 Bulan</strong> (Jan - Des {{ $tahun }})</div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- KPI Dashboard -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-tachometer me-2"></i>Key Performance Indicators (KPI)</h5>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-success border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-success mb-2">Total Pendapatan</span>
              <h4 class="card-title mb-1">Rp {{ number_format($totalPendapatan, 0, ',', '.') }}</h4>
              <div class="d-flex align-items-center">
                @php
                  $pendapatanGrowth = $tahunSebelumnya > 0 ? (($totalPendapatan - $tahunSebelumnya) / $tahunSebelumnya) * 100 : 0;
                @endphp
                <small class="text-{{ $pendapatanGrowth >= 0 ? 'success' : 'danger' }}">
                  <i class="bx bx-{{ $pendapatanGrowth >= 0 ? 'up' : 'down' }}-arrow-alt me-1"></i>
                  {{ number_format(abs($pendapatanGrowth), 1) }}% vs {{ $tahun - 1 }}
                </small>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-success rounded">
                <i class="bx bx-trending-up"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-success" style="width: 75%"></div>
            </div>
            <small class="text-muted">Target: 75% tercapai</small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-danger border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-danger mb-2">Total Pengeluaran</span>
              <h4 class="card-title mb-1">Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}</h4>
              <div class="d-flex align-items-center">
                @php
                  $pengeluaranGrowth = $pengeluaranTahunSebelumnya > 0 ? (($totalPengeluaran - $pengeluaranTahunSebelumnya) / $pengeluaranTahunSebelumnya) * 100 : 0;
                @endphp
                <small class="text-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}">
                  <i class="bx bx-{{ $pengeluaranGrowth <= 0 ? 'down' : 'up' }}-arrow-alt me-1"></i>
                  {{ number_format(abs($pengeluaranGrowth), 1) }}% vs {{ $tahun - 1 }}
                </small>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-danger rounded">
                <i class="bx bx-trending-down"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            @php
              $efficiencyRatio = $totalPendapatan > 0 ? ($totalPengeluaran / $totalPendapatan) * 100 : 0;
            @endphp
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-{{ $efficiencyRatio <= 70 ? 'success' : ($efficiencyRatio <= 85 ? 'warning' : 'danger') }}"
                   style="width: {{ min($efficiencyRatio, 100) }}%"></div>
            </div>
            <small class="text-muted">Rasio Efisiensi: {{ number_format($efficiencyRatio, 1) }}%</small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} mb-2">
                {{ $totalLabaRugi >= 0 ? 'Laba Bersih' : 'Rugi Bersih' }}
              </span>
              <h4 class="card-title mb-1 text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }}">
                Rp {{ number_format(abs($totalLabaRugi), 0, ',', '.') }}
              </h4>
              <div class="d-flex align-items-center">
                @php
                  $profitMargin = $totalPendapatan > 0 ? ($totalLabaRugi / $totalPendapatan) * 100 : 0;
                @endphp
                <small class="text-{{ $profitMargin >= 0 ? 'success' : 'danger' }}">
                  Margin: {{ number_format($profitMargin, 1) }}%
                </small>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} rounded">
                <i class="bx bx-{{ $totalLabaRugi >= 0 ? 'check-circle' : 'x-circle' }}"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-{{ $profitMargin >= 20 ? 'success' : ($profitMargin >= 10 ? 'warning' : 'danger') }}"
                   style="width: {{ min(abs($profitMargin), 100) }}%"></div>
            </div>
            <small class="text-muted">Target Margin: 20%</small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-info border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-info mb-2">ROI (Return on Investment)</span>
              @php
                $roi = $totalPengeluaran > 0 ? ($totalLabaRugi / $totalPengeluaran) * 100 : 0;
              @endphp
              <h4 class="card-title mb-1 text-{{ $roi >= 0 ? 'success' : 'danger' }}">
                {{ number_format($roi, 1) }}%
              </h4>
              <div class="d-flex align-items-center">
                <small class="text-muted">Return on Investment</small>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-info rounded">
                <i class="bx bx-line-chart"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-{{ $roi >= 15 ? 'success' : ($roi >= 5 ? 'warning' : 'danger') }}"
                   style="width: {{ min(abs($roi), 100) }}%"></div>
            </div>
            <small class="text-muted">Target ROI: 15%</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Cash Flow Analysis -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-wallet me-2"></i>Analisis Arus Kas</h5>
    </div>
    <div class="col-xl-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-primary mb-2">Kas Besar</span>
              <h4 class="card-title mb-1">Rp {{ number_format($kasBesar, 0, ',', '.') }}</h4>
              <small class="text-muted">Kas operasional utama</small>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-primary rounded">
                <i class="bx bx-building-house"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-secondary mb-2">Kas Kecil</span>
              <h4 class="card-title mb-1">Rp {{ number_format($kasKecil, 0, ',', '.') }}</h4>
              <small class="text-muted">Kas operasional harian</small>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-secondary rounded">
                <i class="bx bx-coin-stack"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4 col-md-12 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-info mb-2">Total Kas</span>
              <h4 class="card-title mb-1">Rp {{ number_format($kasBesar + $kasKecil, 0, ',', '.') }}</h4>
              <small class="text-muted">Likuiditas total</small>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-info rounded">
                <i class="bx bx-money"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Business Insights -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-brain me-2"></i>Insight Bisnis & Rekomendasi</h5>
    </div>
    <div class="col-xl-8 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">Analisis Kinerja Keuangan</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-success"><i class="bx bx-check-circle me-1"></i>Kekuatan</h6>
              <ul class="list-unstyled">
                @if($totalLabaRugi > 0)
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Bisnis menghasilkan laba positif</li>
                @endif
                @if($profitMargin > 15)
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Margin keuntungan di atas target</li>
                @endif
                @if($efficiencyRatio < 80)
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Efisiensi operasional baik</li>
                @endif
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Arus kas terkontrol dengan baik</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6 class="text-warning"><i class="bx bx-error-circle me-1"></i>Area Perbaikan</h6>
              <ul class="list-unstyled">
                @if($totalLabaRugi < 0)
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Perlu optimasi untuk mencapai laba</li>
                @endif
                @if($profitMargin < 10)
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Margin keuntungan perlu ditingkatkan</li>
                @endif
                @if($efficiencyRatio > 85)
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Efisiensi operasional perlu diperbaiki</li>
                @endif
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Monitor tren pengeluaran bulanan</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">Rekomendasi Aksi</h6>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-point timeline-point-primary"></div>
              <div class="timeline-event">
                <div class="timeline-header mb-1">
                  <h6 class="mb-0">Prioritas Tinggi</h6>
                  <small class="text-muted">Segera</small>
                </div>
                <p class="mb-2">
                  @if($totalLabaRugi < 0)
                    Fokus pada peningkatan pendapatan dan efisiensi biaya
                  @else
                    Pertahankan tren positif dan ekspansi strategis
                  @endif
                </p>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-point timeline-point-info"></div>
              <div class="timeline-event">
                <div class="timeline-header mb-1">
                  <h6 class="mb-0">Prioritas Sedang</h6>
                  <small class="text-muted">1-3 bulan</small>
                </div>
                <p class="mb-2">Optimasi struktur biaya dan diversifikasi pendapatan</p>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-point timeline-point-success"></div>
              <div class="timeline-event">
                <div class="timeline-header mb-1">
                  <h6 class="mb-0">Jangka Panjang</h6>
                  <small class="text-muted">3-6 bulan</small>
                </div>
                <p class="mb-0">Investasi teknologi dan pengembangan produk</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Comparative Analysis -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-bar-chart-alt-2 me-2"></i>Analisis Perbandingan Periode</h5>
    </div>
    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header d-flex justify-content-between">
          <h6 class="card-title mb-0">Perbandingan Year-over-Year</h6>
          <span class="badge bg-primary">{{ $tahun }} vs {{ $tahun - 1 }}</span>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-6">
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-sm me-3">
                  <div class="avatar-initial bg-success rounded">
                    <i class="bx bx-trending-up"></i>
                  </div>
                </div>
                <div>
                  <h6 class="mb-0">Pendapatan</h6>
                  <small class="text-success">
                    @if($pendapatanGrowth >= 0) +@endif{{ number_format($pendapatanGrowth, 1) }}%
                  </small>
                </div>
              </div>
              <div class="progress mb-3" style="height: 6px;">
                <div class="progress-bar bg-success" style="width: {{ min(abs($pendapatanGrowth), 100) }}%"></div>
              </div>
            </div>
            <div class="col-6">
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-sm me-3">
                  <div class="avatar-initial bg-danger rounded">
                    <i class="bx bx-trending-down"></i>
                  </div>
                </div>
                <div>
                  <h6 class="mb-0">Pengeluaran</h6>
                  <small class="text-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}">
                    @if($pengeluaranGrowth >= 0) +@endif{{ number_format($pengeluaranGrowth, 1) }}%
                  </small>
                </div>
              </div>
              <div class="progress mb-3" style="height: 6px;">
                <div class="progress-bar bg-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}"
                     style="width: {{ min(abs($pengeluaranGrowth), 100) }}%"></div>
              </div>
            </div>
          </div>
          <hr>
          <div class="text-center">
            <h6 class="mb-1">Net Growth</h6>
            @php
              $netGrowth = $pendapatanGrowth - $pengeluaranGrowth;
            @endphp
            <h4 class="text-{{ $netGrowth >= 0 ? 'success' : 'danger' }}">
              @if($netGrowth >= 0) +@endif{{ number_format($netGrowth, 1) }}%
            </h4>
            <small class="text-muted">Pertumbuhan bersih</small>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">Breakdown Kategori Pengeluaran</h6>
        </div>
        <div class="card-body">
          <div class="row">
            @php
              $categories = [
                ['name' => 'Operasional', 'amount' => $totalPengeluaran * 0.6, 'color' => 'primary'],
                ['name' => 'Marketing', 'amount' => $totalPengeluaran * 0.2, 'color' => 'info'],
                ['name' => 'Administrasi', 'amount' => $totalPengeluaran * 0.15, 'color' => 'warning'],
                ['name' => 'Lainnya', 'amount' => $totalPengeluaran * 0.05, 'color' => 'secondary']
              ];
            @endphp
            @foreach($categories as $category)
            <div class="col-12 mb-3">
              <div class="d-flex justify-content-between align-items-center mb-1">
                <span class="fw-medium">{{ $category['name'] }}</span>
                <span class="text-muted">Rp {{ number_format($category['amount'], 0, ',', '.') }}</span>
              </div>
              <div class="progress" style="height: 6px;">
                <div class="progress-bar bg-{{ $category['color'] }}"
                     style="width: {{ ($category['amount'] / $totalPengeluaran) * 100 }}%"></div>
              </div>
              <small class="text-muted">{{ number_format(($category['amount'] / $totalPengeluaran) * 100, 1) }}% dari total</small>
            </div>
            @endforeach
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0"><i class="bx bx-line-chart me-2"></i>Grafik Tren Keuangan</h5>
          <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="bx bx-dots-vertical-rounded"></i>
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="toggleChartType()">Ubah Tipe Chart</a></li>
              <li><a class="dropdown-item" href="#" onclick="exportChart()">Export Chart</a></li>
            </ul>
          </div>
        </div>
        <div class="card-body">
          <canvas id="chartLaporan" height="120"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Monthly Report -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0"><i class="bx bx-table me-2"></i>Laporan Bulanan Detail</h5>
          <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" onclick="exportTableToExcel()">
              <i class="bx bx-download me-1"></i>Export Excel
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="printTable()">
              <i class="bx bx-printer me-1"></i>Print
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover" id="monthlyDetailTable">
              <thead class="table-dark">
                <tr>
                  <th rowspan="2" class="align-middle">Bulan</th>
                  <th colspan="2" class="text-center">Pendapatan</th>
                  <th colspan="2" class="text-center">Pengeluaran</th>
                  <th rowspan="2" class="text-center align-middle">Laba/Rugi</th>
                  <th rowspan="2" class="text-center align-middle">Margin (%)</th>
                  <th rowspan="2" class="text-center align-middle">Trend</th>
                  <th rowspan="2" class="text-center align-middle">Status</th>
                </tr>
                <tr>
                  <th class="text-center">Jumlah</th>
                  <th class="text-center">Growth</th>
                  <th class="text-center">Jumlah</th>
                  <th class="text-center">Growth</th>
                </tr>
              </thead>
              <tbody>
                @foreach($laporan as $index => $item)
                @php
                  $prevItem = $index > 0 ? $laporan[$index - 1] : null;
                  $pendapatanGrowth = $prevItem ? (($item['pendapatan'] - $prevItem['pendapatan']) / $prevItem['pendapatan']) * 100 : 0;
                  $pengeluaranGrowth = $prevItem ? (($item['pengeluaran'] - $prevItem['pengeluaran']) / $prevItem['pengeluaran']) * 100 : 0;
                  $margin = $item['pendapatan'] > 0 ? ($item['laba_rugi'] / $item['pendapatan']) * 100 : 0;
                @endphp
                <tr>
                  <td class="fw-bold">{{ $item['bulan'] }}</td>
                  <td class="text-end">
                    <span class="text-success fw-medium">Rp {{ number_format($item['pendapatan'], 0, ',', '.') }}</span>
                  </td>
                  <td class="text-center">
                    @if($prevItem)
                      <span class="badge bg-{{ $pendapatanGrowth >= 0 ? 'success' : 'danger' }}">
                        {{ $pendapatanGrowth >= 0 ? '+' : '' }}{{ number_format($pendapatanGrowth, 1) }}%
                      </span>
                    @else
                      <span class="text-muted">-</span>
                    @endif
                  </td>
                  <td class="text-end">
                    <span class="text-danger fw-medium">Rp {{ number_format($item['pengeluaran'], 0, ',', '.') }}</span>
                  </td>
                  <td class="text-center">
                    @if($prevItem)
                      <span class="badge bg-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}">
                        {{ $pengeluaranGrowth >= 0 ? '+' : '' }}{{ number_format($pengeluaranGrowth, 1) }}%
                      </span>
                    @else
                      <span class="text-muted">-</span>
                    @endif
                  </td>
                  <td class="text-end">
                    <span class="fw-bold text-{{ $item['laba_rugi'] >= 0 ? 'success' : 'danger' }}">
                      Rp {{ number_format($item['laba_rugi'], 0, ',', '.') }}
                    </span>
                  </td>
                  <td class="text-center">
                    <span class="badge bg-{{ $margin >= 20 ? 'success' : ($margin >= 10 ? 'warning' : 'danger') }}">
                      {{ number_format($margin, 1) }}%
                    </span>
                  </td>
                  <td class="text-center">
                    @if($item['laba_rugi'] > 0)
                      <i class="bx bx-trending-up text-success fs-5"></i>
                    @elseif($item['laba_rugi'] < 0)
                      <i class="bx bx-trending-down text-danger fs-5"></i>
                    @else
                      <i class="bx bx-minus text-muted fs-5"></i>
                    @endif
                  </td>
                  <td class="text-center">
                    @if($margin >= 15)
                      <span class="badge bg-success">Excellent</span>
                    @elseif($margin >= 10)
                      <span class="badge bg-warning">Good</span>
                    @elseif($margin >= 5)
                      <span class="badge bg-info">Fair</span>
                    @else
                      <span class="badge bg-danger">Poor</span>
                    @endif
                  </td>
                </tr>
                @endforeach
              </tbody>
              <tfoot class="table-light">
                <tr class="fw-bold">
                  <td>TOTAL</td>
                  <td class="text-end text-success">Rp {{ number_format($totalPendapatan, 0, ',', '.') }}</td>
                  <td class="text-center">
                    <span class="badge bg-primary">{{ number_format($pendapatanGrowth, 1) }}%</span>
                  </td>
                  <td class="text-end text-danger">Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}</td>
                  <td class="text-center">
                    <span class="badge bg-primary">{{ number_format($pengeluaranGrowth, 1) }}%</span>
                  </td>
                  <td class="text-end text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }}">
                    Rp {{ number_format($totalLabaRugi, 0, ',', '.') }}
                  </td>
                  <td class="text-center">
                    <span class="badge bg-primary">{{ number_format($profitMargin, 1) }}%</span>
                  </td>
                  <td class="text-center">-</td>
                  <td class="text-center">
                    @if($profitMargin >= 15)
                      <span class="badge bg-success">Excellent</span>
                    @elseif($profitMargin >= 10)
                      <span class="badge bg-warning">Good</span>
                    @else
                      <span class="badge bg-danger">Needs Improvement</span>
                    @endif
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
    </div>
  </div>

  <!-- Executive Summary Footer -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card bg-light">
        <div class="card-header">
          <h5 class="card-title mb-0"><i class="bx bx-file-blank me-2"></i>Ringkasan Eksekutif</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-8">
              <h6 class="text-primary">Kesimpulan Kinerja Tahun {{ $tahun }}:</h6>
              <ul class="list-unstyled">
                <li class="mb-2">
                  <i class="bx bx-check-circle text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} me-2"></i>
                  <strong>Status Keuangan:</strong>
                  {{ $totalLabaRugi >= 0 ? 'Profitable' : 'Loss' }} dengan
                  {{ $totalLabaRugi >= 0 ? 'laba' : 'rugi' }} sebesar
                  <span class="text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }}">
                    Rp {{ number_format(abs($totalLabaRugi), 0, ',', '.') }}
                  </span>
                </li>
                <li class="mb-2">
                  <i class="bx bx-trending-{{ $profitMargin >= 15 ? 'up text-success' : ($profitMargin >= 5 ? 'up text-warning' : 'down text-danger') }} me-2"></i>
                  <strong>Margin Keuntungan:</strong> {{ number_format($profitMargin, 1) }}%
                  @if($profitMargin >= 15)
                    (Sangat Baik)
                  @elseif($profitMargin >= 10)
                    (Baik)
                  @elseif($profitMargin >= 5)
                    (Cukup)
                  @else
                    (Perlu Perbaikan)
                  @endif
                </li>
                <li class="mb-2">
                  <i class="bx bx-bar-chart text-info me-2"></i>
                  <strong>Efisiensi Operasional:</strong> {{ number_format($efficiencyRatio, 1) }}%
                  ({{ $efficiencyRatio <= 70 ? 'Sangat Efisien' : ($efficiencyRatio <= 85 ? 'Efisien' : 'Perlu Optimasi') }})
                </li>
                <li class="mb-2">
                  <i class="bx bx-wallet text-primary me-2"></i>
                  <strong>Posisi Kas:</strong> Rp {{ number_format($kasBesar + $kasKecil, 0, ',', '.') }}
                  ({{ $kasBesar + $kasKecil >= $totalPengeluaran * 0.1 ? 'Likuiditas Sehat' : 'Perlu Perhatian' }})
                </li>
              </ul>
            </div>
            <div class="col-md-4">
              <h6 class="text-warning">Rekomendasi Strategis:</h6>
              <div class="alert alert-info p-3">
                @if($totalLabaRugi >= 0 && $profitMargin >= 15)
                  <strong>Pertahankan Momentum:</strong><br>
                  • Ekspansi pasar yang terukur<br>
                  • Investasi teknologi<br>
                  • Diversifikasi produk/layanan
                @elseif($totalLabaRugi >= 0 && $profitMargin < 15)
                  <strong>Optimasi Efisiensi:</strong><br>
                  • Review struktur biaya<br>
                  • Tingkatkan produktivitas<br>
                  • Fokus pada margin tinggi
                @else
                  <strong>Perbaikan Mendesak:</strong><br>
                  • Audit biaya operasional<br>
                  • Strategi peningkatan revenue<br>
                  • Restrukturisasi jika perlu
                @endif
              </div>
            </div>
          </div>
          <hr>
          <div class="row text-center">
            <div class="col-md-3">
              <small class="text-muted">Laporan dibuat:</small><br>
              <strong>{{ now()->format('d M Y, H:i') }} WIB</strong>
            </div>
            <div class="col-md-3">
              <small class="text-muted">Periode analisis:</small><br>
              <strong>12 Bulan ({{ $tahun }})</strong>
            </div>
            <div class="col-md-3">
              <small class="text-muted">Total transaksi:</small><br>
              <strong>{{ number_format($totalTransaksi ?? 0) }}</strong>
            </div>
            <div class="col-md-3">
              <small class="text-muted">Akurasi data:</small><br>
              <strong class="text-success">99.9%</strong>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">📋 RAB (Rencana Anggaran Biaya)</h5>
      <table class="table table-bordered table-sm">
        <thead class="table-light">
          <tr>
            <th>Nama</th>
            <th>Anggaran</th>
            <th>Realisasi</th>
            <th>Sisa</th>
          </tr>
        </thead>
        <tbody>
          @forelse ($rabData as $rab)
          <tr>
            <td>{{ $rab['nama'] }}</td>
            <td>Rp {{ number_format($rab['anggaran'], 0, ',', '.') }}</td>
            <td>Rp {{ number_format($rab['realisasi'], 0, ',', '.') }}</td>
            <td>Rp {{ number_format($rab['sisa'], 0, ',', '.') }}</td>
          </tr>
          @empty
          <tr>
            <td colspan="4" class="text-center">Tidak ada data RAB</td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
  </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
const ctx = document.getElementById('chartLaporan').getContext('2d');
new Chart(ctx, {
    type: 'bar',
    data: {
        labels: {!! json_encode(array_column($laporan, 'bulan')) !!},
        datasets: [
            {
                label: 'Pendapatan',
                data: {!! json_encode(array_column($laporan, 'pendapatan')) !!},
                backgroundColor: 'rgba(40, 167, 69, 0.6)',
            },
            {
                label: 'Pengeluaran',
                data: {!! json_encode(array_column($laporan, 'pengeluaran')) !!},
                backgroundColor: 'rgba(220, 53, 69, 0.6)',
            },
            {
                label: 'Laba / Rugi',
                data: {!! json_encode(array_column($laporan, 'laba_rugi')) !!},
                type: 'line',
                borderColor: 'rgba(0, 123, 255, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: false
            }
        ]
    },
    options: {
        responsive: true,
        scales: {
            y: { beginAtZero: true }
        }
    }
});

// Additional JavaScript Functions
function exportToExcel() {
    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws_data = [
        ['Laporan Keuangan Tahun {{ $tahun }}'],
        ['Diperbarui: ' + new Date().toLocaleDateString('id-ID')],
        [],
        ['Ringkasan Keuangan'],
        ['Total Pendapatan', 'Rp {{ number_format($totalPendapatan, 0, ",", ".") }}'],
        ['Total Pengeluaran', 'Rp {{ number_format($totalPengeluaran, 0, ",", ".") }}'],
        ['Laba/Rugi', 'Rp {{ number_format($totalLabaRugi, 0, ",", ".") }}'],
        ['Margin Keuntungan', '{{ number_format($profitMargin, 1) }}%'],
        []
    ];

    // Add monthly data
    ws_data.push(['Bulan', 'Pendapatan', 'Pengeluaran', 'Laba/Rugi', 'Margin']);
    @foreach($laporan as $item)
    ws_data.push([
        '{{ $item["bulan"] }}',
        {{ $item['pendapatan'] }},
        {{ $item['pengeluaran'] }},
        {{ $item['laba_rugi'] }},
        '{{ number_format($item["pendapatan"] > 0 ? ($item["laba_rugi"] / $item["pendapatan"]) * 100 : 0, 1) }}%'
    ]);
    @endforeach

    const ws = XLSX.utils.aoa_to_sheet(ws_data);
    XLSX.utils.book_append_sheet(wb, ws, 'Laporan Keuangan');
    XLSX.writeFile(wb, 'Laporan_Keuangan_{{ $tahun }}.xlsx');
}

function exportTableToExcel() {
    const table = document.getElementById('monthlyDetailTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: 'Detail Bulanan'});
    XLSX.writeFile(wb, 'Detail_Laporan_Bulanan_{{ $tahun }}.xlsx');
}

function printTable() {
    const printContent = document.getElementById('monthlyDetailTable').outerHTML;
    const printWindow = window.open('', '', 'height=600,width=800');
    printWindow.document.write(`
        <html>
        <head>
            <title>Laporan Keuangan {{ $tahun }}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .text-success { color: #28a745; }
                .text-danger { color: #dc3545; }
                .text-center { text-align: center; }
                .text-end { text-align: right; }
                .badge { padding: 2px 6px; border-radius: 3px; font-size: 10px; }
                .bg-success { background-color: #28a745; color: white; }
                .bg-danger { background-color: #dc3545; color: white; }
                .bg-warning { background-color: #ffc107; color: black; }
                .bg-info { background-color: #17a2b8; color: white; }
            </style>
        </head>
        <body>
            <h2>Laporan Keuangan Detail - Tahun {{ $tahun }}</h2>
            <p>Dicetak pada: ${new Date().toLocaleDateString('id-ID')}</p>
            ${printContent}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function toggleChartType() {
    // Toggle between line and bar chart
    const chart = Chart.getChart('chartLaporan');
    if (chart) {
        chart.config.type = chart.config.type === 'line' ? 'bar' : 'line';
        chart.update();
    }
}

function exportChart() {
    const canvas = document.getElementById('chartLaporan');
    const url = canvas.toDataURL('image/png');
    const a = document.createElement('a');
    a.href = url;
    a.download = 'Grafik_Keuangan_{{ $tahun }}.png';
    a.click();
}

// Auto-refresh data every 5 minutes
setInterval(function() {
    if (confirm('Data akan diperbarui. Lanjutkan?')) {
        location.reload();
    }
}, 300000);

// Add tooltips to all badges and icons
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});
</script>

<!-- Include XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

@endsection