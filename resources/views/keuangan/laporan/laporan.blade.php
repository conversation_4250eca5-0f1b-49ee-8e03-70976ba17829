@extends('layouts.contentNavbarLayout')

@section('title', '<PERSON>por<PERSON>')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-style1">
            <li class="breadcrumb-item">
                <a href="{{ route('dashboard-keuangan') }}">Dashboard</a>
            </li>
            <li class="breadcrumb-item active"><PERSON><PERSON><PERSON>uang<PERSON></li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bx bx-chart me-2"></i><PERSON><PERSON><PERSON>f
                    </h4>
                    <div class="d-flex gap-2">
                        <span class="badge bg-primary"><PERSON>hun {{ $currentYear }}</span>
                        <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                            <i class="bx bx-printer me-1"></i>Cetak
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="badge bg-label-success mb-2">Total Pendapatan</span>
                            <h4 class="card-title mb-1">Rp {{ number_format($totalRevenue, 0, ',', '.') }}</h4>
                            <small class="text-muted">Pembayaran + Pendapatan Lain</small>
                        </div>
                        <div class="avatar">
                            <div class="avatar-initial bg-success rounded">
                                <i class="bx bx-trending-up"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="badge bg-label-danger mb-2">Total Pengeluaran</span>
                            <h4 class="card-title mb-1">Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}</h4>
                            <small class="text-muted">Semua Pengeluaran</small>
                        </div>
                        <div class="avatar">
                            <div class="avatar-initial bg-danger rounded">
                                <i class="bx bx-trending-down"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="badge bg-label-{{ $profitLoss >= 0 ? 'success' : 'danger' }} mb-2">
                                {{ $profitLoss >= 0 ? 'Laba' : 'Rugi' }}
                            </span>
                            <h4 class="card-title mb-1">Rp {{ number_format($profitLoss, 0, ',', '.') }}</h4>
                            <small class="text-muted">Pendapatan - Pengeluaran</small>
                        </div>
                        <div class="avatar">
                            <div class="avatar-initial bg-{{ $profitLoss >= 0 ? 'success' : 'danger' }} rounded">
                                <i class="bx bx-{{ $profitLoss >= 0 ? 'check-circle' : 'x-circle' }}"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="badge bg-label-info mb-2">Kas Bersih</span>
                            <h4 class="card-title mb-1">Rp {{ number_format($kasBersih, 0, ',', '.') }}</h4>
                            <small class="text-muted">Debit - Kredit</small>
                        </div>
                        <div class="avatar">
                            <div class="avatar-initial bg-info rounded">
                                <i class="bx bx-wallet"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Revenue vs Expense Chart -->
        <div class="col-xl-8 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between">
                    <h5 class="card-title mb-0">Grafik Pendapatan vs Pengeluaran (6 Bulan Terakhir)</h5>
                </div>
                <div class="card-body">
                    <div style="height: 300px;">
                        <canvas id="revenueExpenseChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profit Chart -->
        <div class="col-xl-4 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Grafik Laba/Rugi</h5>
                </div>
                <div class="card-body">
                    <div style="height: 300px;">
                        <canvas id="profitChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- RAB & Cash Flow -->
    <div class="row mb-4">
        <!-- RAB Summary -->
        <div class="col-xl-6 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Ringkasan RAB (Rencana Anggaran Belanja)</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar avatar-sm me-3">
                                    <div class="avatar-initial bg-primary rounded">
                                        <i class="bx bx-receipt"></i>
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-0">Total Anggaran</h6>
                                    <small class="text-muted">Rp {{ number_format($totalRAB, 0, ',', '.') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar avatar-sm me-3">
                                    <div class="avatar-initial bg-warning rounded">
                                        <i class="bx bx-credit-card"></i>
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-0">Terpakai</h6>
                                    <small class="text-muted">Rp {{ number_format($usedRAB, 0, ',', '.') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-3">
                                    <div class="avatar-initial bg-success rounded">
                                        <i class="bx bx-check-circle"></i>
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-0">Sisa Anggaran</h6>
                                    <small class="text-muted">Rp {{ number_format($remainingRAB, 0, ',', '.') }}</small>
                                </div>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar" role="progressbar"
                                     style="width: {{ $totalRAB > 0 ? ($usedRAB / $totalRAB) * 100 : 0 }}%">
                                </div>
                            </div>
                            <small class="text-muted">
                                {{ $totalRAB > 0 ? number_format(($usedRAB / $totalRAB) * 100, 1) : 0 }}% terpakai
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cash Flow -->
        <div class="col-xl-6 col-12 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Arus Kas</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-4">
                            <div class="text-center">
                                <div class="avatar avatar-lg mx-auto mb-2">
                                    <div class="avatar-initial bg-success rounded">
                                        <i class="bx bx-plus-circle"></i>
                                    </div>
                                </div>
                                <h6 class="mb-0">Kas Masuk</h6>
                                <small class="text-success">Rp {{ number_format($totalRevenue, 0, ',', '.') }}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <div class="avatar avatar-lg mx-auto mb-2">
                                    <div class="avatar-initial bg-danger rounded">
                                        <i class="bx bx-minus-circle"></i>
                                    </div>
                                </div>
                                <h6 class="mb-0">Kas Keluar</h6>
                                <small class="text-danger">Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <div class="avatar avatar-lg mx-auto mb-2">
                                    <div class="avatar-initial bg-info rounded">
                                        <i class="bx bx-wallet"></i>
                                    </div>
                                </div>
                                <h6 class="mb-0">Saldo Kas</h6>
                                <small class="text-info">Rp {{ number_format($kasBersih, 0, ',', '.') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Comparison Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Perbandingan Pendapatan Bulanan ({{ $currentYear }} vs {{ $currentYear - 1 }})</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bx bx-dots-vertical-rounded"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportTable('monthlyTable')">Export Excel</a></li>
                            <li><a class="dropdown-item" href="#" onclick="printTable('monthlyTable')">Print</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="monthlyTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Bulan</th>
                                    <th class="text-end">{{ $currentYear }}</th>
                                    <th class="text-end">{{ $currentYear - 1 }}</th>
                                    <th class="text-end">Selisih</th>
                                    <th class="text-center">Persentase</th>
                                    <th class="text-center">Trend</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $monthNames = [
                                        1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
                                        5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
                                        9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
                                    ];
                                @endphp
                                @foreach($monthlyComparison as $data)
                                <tr>
                                    <td>
                                        <strong>{{ $monthNames[$data['month']] }}</strong>
                                    </td>
                                    <td class="text-end">
                                        <span class="text-primary">Rp {{ number_format($data['current_year'], 0, ',', '.') }}</span>
                                    </td>
                                    <td class="text-end">
                                        <span class="text-muted">Rp {{ number_format($data['previous_year'], 0, ',', '.') }}</span>
                                    </td>
                                    <td class="text-end">
                                        <span class="text-{{ $data['difference'] >= 0 ? 'success' : 'danger' }}">
                                            {{ $data['difference'] >= 0 ? '+' : '' }}Rp {{ number_format($data['difference'], 0, ',', '.') }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        @php
                                            $percentage = $data['previous_year'] > 0 ? (($data['difference'] / $data['previous_year']) * 100) : 0;
                                        @endphp
                                        <span class="badge bg-{{ $percentage >= 0 ? 'success' : 'danger' }}">
                                            {{ $percentage >= 0 ? '+' : '' }}{{ number_format($percentage, 1) }}%
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        @if($data['difference'] > 0)
                                            <i class="bx bx-trending-up text-success"></i>
                                        @elseif($data['difference'] < 0)
                                            <i class="bx bx-trending-down text-danger"></i>
                                        @else
                                            <i class="bx bx-minus text-muted"></i>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('page-script')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Wait for Chart.js to load completely
function initCharts() {
    console.log('Chart.js loaded:', typeof Chart !== 'undefined');
    console.log('Revenue data:', @json($revenueChart));
    console.log('Expense data:', @json($expenseChart));
    console.log('Profit data:', @json($profitChart));
    // Helper function to get last 6 months labels
    function getLastSixMonthsLabels() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
                       'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];
        const labels = [];
        const currentDate = new Date();

        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            labels.push(months[date.getMonth()]);
        }

        return labels;
    }

    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded');
        return;
    }

    // Revenue vs Expense Chart
    const revenueExpenseCtx = document.getElementById('revenueExpenseChart');
    if (revenueExpenseCtx) {
        try {
        const revenueExpenseChart = new Chart(revenueExpenseCtx, {
            type: 'line',
            data: {
                labels: getLastSixMonthsLabels(),
                datasets: [{
                    label: 'Pendapatan',
                    data: @json($revenueChart),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Pengeluaran',
                    data: @json($expenseChart),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': Rp ' +
                                       new Intl.NumberFormat('id-ID').format(context.parsed.y);
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Bulan'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Jumlah (Rp)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
        } catch (error) {
            console.error('Error creating revenue expense chart:', error);
        }
    }

    // Profit Chart
    const profitCtx = document.getElementById('profitChart');
    if (profitCtx) {
        try {
        const profitData = @json($profitChart);
        const profitChart = new Chart(profitCtx, {
            type: 'bar',
            data: {
                labels: getLastSixMonthsLabels(),
                datasets: [{
                    label: 'Laba/Rugi',
                    data: profitData,
                    backgroundColor: profitData.map(value =>
                        value >= 0 ? 'rgba(40, 167, 69, 0.8)' : 'rgba(220, 53, 69, 0.8)'
                    ),
                    borderColor: profitData.map(value =>
                        value >= 0 ? '#28a745' : '#dc3545'
                    ),
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const status = value >= 0 ? 'Laba' : 'Rugi';
                                return status + ': Rp ' + new Intl.NumberFormat('id-ID').format(Math.abs(value));
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Bulan'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Laba/Rugi (Rp)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                            }
                        }
                    }
                }
            }
        });
        } catch (error) {
            console.error('Error creating profit chart:', error);
        }
    }

    // Export table function
    window.exportTable = function(tableId) {
        const table = document.getElementById(tableId);
        let csv = [];
        const rows = table.querySelectorAll('tr');

        for (let i = 0; i < rows.length; i++) {
            const row = [], cols = rows[i].querySelectorAll('td, th');

            for (let j = 0; j < cols.length - 1; j++) { // Exclude last column (trend icons)
                row.push(cols[j].innerText);
            }

            csv.push(row.join(','));
        }

        const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
        const downloadLink = document.createElement('a');
        downloadLink.download = 'laporan-keuangan.csv';
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    };

    // Print table function
    window.printTable = function(tableId) {
        const table = document.getElementById(tableId);
        const printWindow = window.open('', '', 'height=600,width=800');

        printWindow.document.write('<html><head><title>Laporan Keuangan</title>');
        printWindow.document.write('<style>table { border-collapse: collapse; width: 100%; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; }</style>');
        printWindow.document.write('</head><body>');
        printWindow.document.write('<h2>Laporan Keuangan - Perbandingan Bulanan</h2>');
        printWindow.document.write(table.outerHTML);
        printWindow.document.write('</body></html>');

        printWindow.document.close();
        printWindow.print();
    };
}

// Initialize charts when DOM is ready and Chart.js is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof Chart !== 'undefined') {
        initCharts();
    } else {
        // Wait for Chart.js to load
        setTimeout(function() {
            if (typeof Chart !== 'undefined') {
                initCharts();
            } else {
                console.error('Chart.js failed to load');
            }
        }, 1000);
    }
});
</script>
@endsection
