@extends('layouts.contentNavbarLayout')

@section('title', 'Lapor<PERSON>')

@section('content')
<div class="container py-4">
  <h4 class="fw-bold mb-3">📊 <PERSON><PERSON><PERSON> {{ $tahun }}</h4>

  <form method="GET" class="row mb-4">
    <div class="col-md-3">
      <select name="tahun" class="form-select" onchange="this.form.submit()">
        @for ($i = now()->year; $i >= 2020; $i--)
        <option value="{{ $i }}" {{ $i == $tahun ? 'selected' : '' }}>{{ $i }}</option>
        @endfor
      </select>
    </div>
  </form>

  <div class="row mb-3">
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h6>Total Pendapatan</h6>
          <h4 class="text-success">Rp {{ number_format($totalPendapatan, 0, ',', '.') }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h6>Total Pengeluaran</h6>
          <h4 class="text-danger">Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h6>Laba / Rugi</h6>
          <h4 class="{{ $totalLabaRugi >= 0 ? 'text-success' : 'text-danger' }}">
            Rp {{ number_format($totalLabaRugi, 0, ',', '.') }}
          </h4>
        </div>
      </div>
    </div>
  </div>

  <div class="row mb-3">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h6>Kas Besar</h6>
          <h5>Rp {{ number_format($kasBesar, 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h6>Kas Kecil</h6>
          <h5>Rp {{ number_format($kasKecil, 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>
  </div>

  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <h5 class="mb-3">📈 Grafik Tren Keuangan</h5>
      <canvas id="chartLaporan" height="120"></canvas>
    </div>
  </div>

  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <h5>📄 Laporan Bulanan</h5>
      <table class="table table-bordered table-sm mt-3">
        <thead class="table-light">
          <tr>
            <th>Bulan</th>
            <th>Pendapatan</th>
            <th>Pengeluaran</th>
            <th>Laba / Rugi</th>
          </tr>
        </thead>
        <tbody>
          @foreach($laporan as $item)
          <tr>
            <td>{{ $item['bulan'] }}</td>
            <td class="text-success">Rp {{ number_format($item['pendapatan'], 0, ',', '.') }}</td>
            <td class="text-danger">Rp {{ number_format($item['pengeluaran'], 0, ',', '.') }}</td>
            <td class="{{ $item['laba_rugi'] >= 0 ? 'text-success' : 'text-danger' }}">
              Rp {{ number_format($item['laba_rugi'], 0, ',', '.') }}
            </td>
          </tr>
          @endforeach
        </tbody>
      </table>
    </div>
  </div>

  <div class="card shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">📋 RAB (Rencana Anggaran Biaya)</h5>
      <table class="table table-bordered table-sm">
        <thead class="table-light">
          <tr>
            <th>Nama</th>
            <th>Anggaran</th>
            <th>Realisasi</th>
            <th>Sisa</th>
          </tr>
        </thead>
        <tbody>
          @forelse ($rabData as $rab)
          <tr>
            <td>{{ $rab['nama'] }}</td>
            <td>Rp {{ number_format($rab['anggaran'], 0, ',', '.') }}</td>
            <td>Rp {{ number_format($rab['realisasi'], 0, ',', '.') }}</td>
            <td>Rp {{ number_format($rab['sisa'], 0, ',', '.') }}</td>
          </tr>
          @empty
          <tr>
            <td colspan="4" class="text-center">Tidak ada data RAB</td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
  </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
const ctx = document.getElementById('chartLaporan').getContext('2d');
new Chart(ctx, {
    type: 'bar',
    data: {
        labels: {!! json_encode(array_column($laporan, 'bulan')) !!},
        datasets: [
            {
                label: 'Pendapatan',
                data: {!! json_encode(array_column($laporan, 'pendapatan')) !!},
                backgroundColor: 'rgba(40, 167, 69, 0.6)',
            },
            {
                label: 'Pengeluaran',
                data: {!! json_encode(array_column($laporan, 'pengeluaran')) !!},
                backgroundColor: 'rgba(220, 53, 69, 0.6)',
            },
            {
                label: 'Laba / Rugi',
                data: {!! json_encode(array_column($laporan, 'laba_rugi')) !!},
                type: 'line',
                borderColor: 'rgba(0, 123, 255, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: false
            }
        ]
    },
    options: {
        responsive: true,
        scales: {
            y: { beginAtZero: true }
        }
    }
});
</script>
@endsection