@extends('layouts.contentNavbarLayout')

@section('title', 'Laporan Keuang<PERSON>')

@section('content')
<div class="container py-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card bg-gradient-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h3 class="card-title mb-1">📊 Laporan Keuangan Komprehensif</h3>
              <p class="mb-0">Periode: Tahun {{ $tahun }} | Diperbarui: {{ now()->format('d M Y, H:i') }} WIB</p>
            </div>
            <div class="text-end">
              <div class="d-flex gap-2">
                <button class="btn btn-light btn-sm" onclick="window.print()">
                  <i class="bx bx-printer me-1"></i>Cetak
                </button>
                <button class="btn btn-light btn-sm" onclick="exportToExcel()">
                  <i class="bx bx-download me-1"></i>Export
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Advanced Filter Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0"><i class="bx bx-filter me-2"></i>Filter & Analisis Lanjutan</h6>
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleAdvancedFilters()">
              <i class="bx bx-cog me-1"></i>Advanced
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetFilters()">
              <i class="bx bx-refresh me-1"></i>Reset
            </button>
            <button type="button" class="btn btn-sm btn-success" onclick="applyFilters()">
              <i class="bx bx-check me-1"></i>Apply
            </button>
          </div>
        </div>
        <div class="card-body">
          <form method="GET" id="filterForm" class="row">
            <!-- Basic Filters -->
            <div class="col-xl-3 col-md-6 mb-3">
              <label class="form-label">Periode Analisis</label>
              <select name="tahun" class="form-select" id="tahunFilter">
                @for ($i = now()->year; $i >= 2020; $i--)
                <option value="{{ $i }}" {{ $i == $tahun ? 'selected' : '' }}>{{ $i }}</option>
                @endfor
              </select>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
              <label class="form-label">Range Bulan</label>
              <select name="bulan_range" class="form-select" id="bulanRange">
                <option value="all">Semua Bulan</option>
                <option value="q1">Q1 (Jan-Mar)</option>
                <option value="q2">Q2 (Apr-Jun)</option>
                <option value="q3">Q3 (Jul-Sep)</option>
                <option value="q4">Q4 (Okt-Des)</option>
                <option value="h1">H1 (Jan-Jun)</option>
                <option value="h2">H2 (Jul-Des)</option>
                <option value="custom">Custom Range</option>
              </select>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
              <label class="form-label">Kategori Analisis</label>
              <select name="kategori" class="form-select" id="kategoriFilter">
                <option value="all">Semua Kategori</option>
                <option value="pendapatan">Fokus Pendapatan</option>
                <option value="pengeluaran">Fokus Pengeluaran</option>
                <option value="profitabilitas">Profitabilitas</option>
                <option value="cashflow">Cash Flow</option>
                <option value="efficiency">Efisiensi</option>
              </select>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
              <label class="form-label">Tipe Visualisasi</label>
              <select name="chart_type" class="form-select" id="chartType">
                <option value="line">Line Chart</option>
                <option value="bar">Bar Chart</option>
                <option value="area">Area Chart</option>
                <option value="combo">Combination</option>
                <option value="pie">Pie Chart</option>
                <option value="donut">Donut Chart</option>
              </select>
            </div>

            <!-- Advanced Filters (Hidden by default) -->
            <div id="advancedFilters" class="col-12" style="display: none;">
              <hr class="my-3">
              <h6 class="text-primary mb-3"><i class="bx bx-cog me-2"></i>Filter Lanjutan</h6>

              <div class="row">
                <div class="col-xl-3 col-md-6 mb-3">
                  <label class="form-label">Custom Date Range</label>
                  <div class="input-group">
                    <input type="date" name="start_date" class="form-control" id="startDate">
                    <span class="input-group-text">to</span>
                    <input type="date" name="end_date" class="form-control" id="endDate">
                  </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                  <label class="form-label">Minimum Amount</label>
                  <div class="input-group">
                    <span class="input-group-text">Rp</span>
                    <input type="number" name="min_amount" class="form-control" placeholder="0">
                  </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                  <label class="form-label">Maximum Amount</label>
                  <div class="input-group">
                    <span class="input-group-text">Rp</span>
                    <input type="number" name="max_amount" class="form-control" placeholder="999999999">
                  </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                  <label class="form-label">Status Filter</label>
                  <select name="status_filter" class="form-select">
                    <option value="all">Semua Status</option>
                    <option value="profit">Hanya Profit</option>
                    <option value="loss">Hanya Loss</option>
                    <option value="break_even">Break Even</option>
                  </select>
                </div>

                <div class="col-xl-4 col-md-6 mb-3">
                  <label class="form-label">Metode Pembayaran</label>
                  <select name="payment_method" class="form-select" multiple>
                    <option value="cash">Cash</option>
                    <option value="transfer">Transfer Bank</option>
                    <option value="credit">Kartu Kredit</option>
                    <option value="ewallet">E-Wallet</option>
                  </select>
                </div>

                <div class="col-xl-4 col-md-6 mb-3">
                  <label class="form-label">Trend Analysis</label>
                  <select name="trend_analysis" class="form-select">
                    <option value="none">No Trend Analysis</option>
                    <option value="growth">Growth Trend</option>
                    <option value="seasonal">Seasonal Pattern</option>
                    <option value="moving_avg">Moving Average</option>
                    <option value="forecast">Forecast Trend</option>
                  </select>
                </div>

                <div class="col-xl-4 col-md-6 mb-3">
                  <label class="form-label">Comparison Mode</label>
                  <select name="comparison" class="form-select">
                    <option value="none">No Comparison</option>
                    <option value="yoy">Year over Year</option>
                    <option value="mom">Month over Month</option>
                    <option value="qoq">Quarter over Quarter</option>
                    <option value="budget">vs Budget</option>
                  </select>
                </div>
              </div>

              <!-- Quick Filter Presets -->
              <div class="row mt-3">
                <div class="col-12">
                  <h6 class="text-info mb-2"><i class="bx bx-bookmark me-2"></i>Quick Presets</h6>
                  <div class="d-flex flex-wrap gap-2">
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="applyPreset('profitable')">
                      <i class="bx bx-trending-up me-1"></i>Most Profitable
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="applyPreset('high_expense')">
                      <i class="bx bx-trending-down me-1"></i>High Expense
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="applyPreset('growth')">
                      <i class="bx bx-line-chart me-1"></i>Growth Analysis
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="applyPreset('seasonal')">
                      <i class="bx bx-calendar me-1"></i>Seasonal Trends
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="applyPreset('efficiency')">
                      <i class="bx bx-cog me-1"></i>Efficiency Focus
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </form>

          <!-- Filter Status & Info -->
          <div class="row mt-3">
            <div class="col-md-8">
              <div class="d-flex align-items-center gap-3">
                <div>
                  <small class="text-muted">Status Sistem:</small>
                  <span class="badge bg-success ms-1" id="systemStatus">
                    <i class="bx bx-wifi me-1"></i>Real-time
                  </span>
                </div>
                <div>
                  <small class="text-muted">Last Update:</small>
                  <span class="fw-medium" id="lastUpdate">{{ now()->format('H:i:s') }}</span>
                  <span class="badge bg-success ms-1" id="updateIndicator">
                    <i class="bx bx-check-circle"></i>
                  </span>
                </div>
                <div>
                  <small class="text-muted">Data Points:</small>
                  <span class="fw-medium" id="dataPoints">{{ number_format($totalTransaksi ?? 0) }}</span>
                  <small class="text-muted ms-1" id="dataGrowth">+0%</small>
                </div>
                <div>
                  <small class="text-muted">Auto-refresh:</small>
                  <div class="form-check form-switch d-inline-block ms-1">
                    <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                    <label class="form-check-label" for="autoRefresh">
                      <small>30s</small>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4 text-end">
              <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-sm btn-outline-info" onclick="saveFilterPreset()">
                  <i class="bx bx-save me-1"></i>Save Preset
                </button>
                <button type="button" class="btn btn-sm btn-outline-warning" onclick="exportFilteredData()">
                  <i class="bx bx-export me-1"></i>Export
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Real-time Dashboard Widget -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card bg-gradient-info text-white">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h6 class="card-title mb-2">
                <i class="bx bx-broadcast me-2"></i>Real-time Financial Monitor
              </h6>
              <div class="row">
                <div class="col-md-3">
                  <div class="d-flex align-items-center">
                    <div class="avatar avatar-sm me-2">
                      <div class="avatar-initial bg-white text-info rounded">
                        <i class="bx bx-trending-up"></i>
                      </div>
                    </div>
                    <div>
                      <small class="opacity-75">Live Revenue</small>
                      <div class="fw-bold" id="liveRevenue">Rp 0</div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="d-flex align-items-center">
                    <div class="avatar avatar-sm me-2">
                      <div class="avatar-initial bg-white text-info rounded">
                        <i class="bx bx-transfer"></i>
                      </div>
                    </div>
                    <div>
                      <small class="opacity-75">Active Transactions</small>
                      <div class="fw-bold" id="activeTransactions">0</div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="d-flex align-items-center">
                    <div class="avatar avatar-sm me-2">
                      <div class="avatar-initial bg-white text-info rounded">
                        <i class="bx bx-time"></i>
                      </div>
                    </div>
                    <div>
                      <small class="opacity-75">Avg Response</small>
                      <div class="fw-bold" id="avgResponse">0ms</div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="d-flex align-items-center">
                    <div class="avatar avatar-sm me-2">
                      <div class="avatar-initial bg-white text-info rounded">
                        <i class="bx bx-pulse"></i>
                      </div>
                    </div>
                    <div>
                      <small class="opacity-75">System Load</small>
                      <div class="fw-bold" id="systemLoad">0%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4 text-end">
              <div class="d-flex justify-content-end align-items-center gap-3">
                <div class="text-center">
                  <div class="spinner-border spinner-border-sm text-white" role="status" id="loadingSpinner" style="display: none;">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div id="connectionStatus">
                    <i class="bx bx-wifi fs-4"></i>
                    <small class="d-block">Connected</small>
                  </div>
                </div>
                <div class="text-center">
                  <canvas id="miniChart" width="80" height="40"></canvas>
                  <small class="d-block">Live Trend</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Live Notifications -->
  <div id="liveNotifications" class="position-fixed" style="top: 80px; right: 20px; z-index: 1050; max-width: 350px;">
    <!-- Notifications will be inserted here -->
  </div>

  <!-- KPI Dashboard -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-tachometer me-2"></i>Key Performance Indicators (KPI)</h5>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-success border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-success mb-2">
                Total Pendapatan
                <span class="badge bg-success ms-1" id="pendapatanIndicator">
                  <i class="bx bx-wifi" style="font-size: 8px;"></i>
                </span>
              </span>
              <h4 class="card-title mb-1" data-realtime="totalPendapatan">
                Rp {{ number_format($totalPendapatan, 0, ',', '.') }}
              </h4>
              <div class="d-flex align-items-center">
                @php
                  $pendapatanGrowth = $tahunSebelumnya > 0 ? (($totalPendapatan - $tahunSebelumnya) / $tahunSebelumnya) * 100 : 0;
                @endphp
                <small class="text-{{ $pendapatanGrowth >= 0 ? 'success' : 'danger' }}" data-realtime="pendapatanGrowth">
                  <i class="bx bx-{{ $pendapatanGrowth >= 0 ? 'up' : 'down' }}-arrow-alt me-1"></i>
                  {{ number_format(abs($pendapatanGrowth), 1) }}% vs {{ $tahun - 1 }}
                </small>
                <span class="ms-2 badge bg-light text-dark" id="pendapatanChange">
                  <i class="bx bx-pulse"></i>
                </span>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-success rounded">
                <i class="bx bx-trending-up"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-success" style="width: 75%"></div>
            </div>
            <small class="text-muted">Target: 75% tercapai</small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-danger border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-danger mb-2">Total Pengeluaran</span>
              <h4 class="card-title mb-1">Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}</h4>
              <div class="d-flex align-items-center">
                @php
                  $pengeluaranGrowth = $pengeluaranTahunSebelumnya > 0 ? (($totalPengeluaran - $pengeluaranTahunSebelumnya) / $pengeluaranTahunSebelumnya) * 100 : 0;
                @endphp
                <small class="text-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}">
                  <i class="bx bx-{{ $pengeluaranGrowth <= 0 ? 'down' : 'up' }}-arrow-alt me-1"></i>
                  {{ number_format(abs($pengeluaranGrowth), 1) }}% vs {{ $tahun - 1 }}
                </small>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-danger rounded">
                <i class="bx bx-trending-down"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            @php
              $efficiencyRatio = $totalPendapatan > 0 ? ($totalPengeluaran / $totalPendapatan) * 100 : 0;
            @endphp
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-{{ $efficiencyRatio <= 70 ? 'success' : ($efficiencyRatio <= 85 ? 'warning' : 'danger') }}"
                   style="width: {{ min($efficiencyRatio, 100) }}%"></div>
            </div>
            <small class="text-muted">Rasio Efisiensi: {{ number_format($efficiencyRatio, 1) }}%</small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} mb-2">
                {{ $totalLabaRugi >= 0 ? 'Laba Bersih' : 'Rugi Bersih' }}
              </span>
              <h4 class="card-title mb-1 text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }}">
                Rp {{ number_format(abs($totalLabaRugi), 0, ',', '.') }}
              </h4>
              <div class="d-flex align-items-center">
                @php
                  $profitMargin = $totalPendapatan > 0 ? ($totalLabaRugi / $totalPendapatan) * 100 : 0;
                @endphp
                <small class="text-{{ $profitMargin >= 0 ? 'success' : 'danger' }}">
                  Margin: {{ number_format($profitMargin, 1) }}%
                </small>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} rounded">
                <i class="bx bx-{{ $totalLabaRugi >= 0 ? 'check-circle' : 'x-circle' }}"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-{{ $profitMargin >= 20 ? 'success' : ($profitMargin >= 10 ? 'warning' : 'danger') }}"
                   style="width: {{ min(abs($profitMargin), 100) }}%"></div>
            </div>
            <small class="text-muted">Target Margin: 20%</small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card h-100 border-start border-info border-4">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-info mb-2">ROI (Return on Investment)</span>
              @php
                $roi = $totalPengeluaran > 0 ? ($totalLabaRugi / $totalPengeluaran) * 100 : 0;
              @endphp
              <h4 class="card-title mb-1 text-{{ $roi >= 0 ? 'success' : 'danger' }}">
                {{ number_format($roi, 1) }}%
              </h4>
              <div class="d-flex align-items-center">
                <small class="text-muted">Return on Investment</small>
              </div>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-info rounded">
                <i class="bx bx-line-chart"></i>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="progress" style="height: 6px;">
              <div class="progress-bar bg-{{ $roi >= 15 ? 'success' : ($roi >= 5 ? 'warning' : 'danger') }}"
                   style="width: {{ min(abs($roi), 100) }}%"></div>
            </div>
            <small class="text-muted">Target ROI: 15%</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Cash Flow Analysis -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-wallet me-2"></i>Analisis Arus Kas</h5>
    </div>
    <div class="col-xl-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-primary mb-2">Kas Besar</span>
              <h4 class="card-title mb-1">Rp {{ number_format($kasBesar, 0, ',', '.') }}</h4>
              <small class="text-muted">Kas operasional utama</small>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-primary rounded">
                <i class="bx bx-building-house"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-secondary mb-2">Kas Kecil</span>
              <h4 class="card-title mb-1">Rp {{ number_format($kasKecil, 0, ',', '.') }}</h4>
              <small class="text-muted">Kas operasional harian</small>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-secondary rounded">
                <i class="bx bx-coin-stack"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4 col-md-12 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <span class="badge bg-label-info mb-2">Total Kas</span>
              <h4 class="card-title mb-1">Rp {{ number_format($kasBesar + $kasKecil, 0, ',', '.') }}</h4>
              <small class="text-muted">Likuiditas total</small>
            </div>
            <div class="avatar">
              <div class="avatar-initial bg-info rounded">
                <i class="bx bx-money"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Business Insights -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-brain me-2"></i>Insight Bisnis & Rekomendasi</h5>
    </div>
    <div class="col-xl-8 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">Analisis Kinerja Keuangan</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-success"><i class="bx bx-check-circle me-1"></i>Kekuatan</h6>
              <ul class="list-unstyled">
                @if($totalLabaRugi > 0)
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Bisnis menghasilkan laba positif</li>
                @endif
                @if($profitMargin > 15)
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Margin keuntungan di atas target</li>
                @endif
                @if($efficiencyRatio < 80)
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Efisiensi operasional baik</li>
                @endif
                <li class="mb-2"><i class="bx bx-chevron-right text-success me-1"></i>Arus kas terkontrol dengan baik</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6 class="text-warning"><i class="bx bx-error-circle me-1"></i>Area Perbaikan</h6>
              <ul class="list-unstyled">
                @if($totalLabaRugi < 0)
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Perlu optimasi untuk mencapai laba</li>
                @endif
                @if($profitMargin < 10)
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Margin keuntungan perlu ditingkatkan</li>
                @endif
                @if($efficiencyRatio > 85)
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Efisiensi operasional perlu diperbaiki</li>
                @endif
                <li class="mb-2"><i class="bx bx-chevron-right text-warning me-1"></i>Monitor tren pengeluaran bulanan</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-4 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">Rekomendasi Aksi</h6>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-point timeline-point-primary"></div>
              <div class="timeline-event">
                <div class="timeline-header mb-1">
                  <h6 class="mb-0">Prioritas Tinggi</h6>
                  <small class="text-muted">Segera</small>
                </div>
                <p class="mb-2">
                  @if($totalLabaRugi < 0)
                    Fokus pada peningkatan pendapatan dan efisiensi biaya
                  @else
                    Pertahankan tren positif dan ekspansi strategis
                  @endif
                </p>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-point timeline-point-info"></div>
              <div class="timeline-event">
                <div class="timeline-header mb-1">
                  <h6 class="mb-0">Prioritas Sedang</h6>
                  <small class="text-muted">1-3 bulan</small>
                </div>
                <p class="mb-2">Optimasi struktur biaya dan diversifikasi pendapatan</p>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-point timeline-point-success"></div>
              <div class="timeline-event">
                <div class="timeline-header mb-1">
                  <h6 class="mb-0">Jangka Panjang</h6>
                  <small class="text-muted">3-6 bulan</small>
                </div>
                <p class="mb-0">Investasi teknologi dan pengembangan produk</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Comparative Analysis -->
  <div class="row mb-4">
    <div class="col-12">
      <h5 class="mb-3"><i class="bx bx-bar-chart-alt-2 me-2"></i>Analisis Perbandingan Periode</h5>
    </div>
    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header d-flex justify-content-between">
          <h6 class="card-title mb-0">Perbandingan Year-over-Year</h6>
          <span class="badge bg-primary">{{ $tahun }} vs {{ $tahun - 1 }}</span>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-6">
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-sm me-3">
                  <div class="avatar-initial bg-success rounded">
                    <i class="bx bx-trending-up"></i>
                  </div>
                </div>
                <div>
                  <h6 class="mb-0">Pendapatan</h6>
                  <small class="text-success">
                    @if($pendapatanGrowth >= 0) +@endif{{ number_format($pendapatanGrowth, 1) }}%
                  </small>
                </div>
              </div>
              <div class="progress mb-3" style="height: 6px;">
                <div class="progress-bar bg-success" style="width: {{ min(abs($pendapatanGrowth), 100) }}%"></div>
              </div>
            </div>
            <div class="col-6">
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-sm me-3">
                  <div class="avatar-initial bg-danger rounded">
                    <i class="bx bx-trending-down"></i>
                  </div>
                </div>
                <div>
                  <h6 class="mb-0">Pengeluaran</h6>
                  <small class="text-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}">
                    @if($pengeluaranGrowth >= 0) +@endif{{ number_format($pengeluaranGrowth, 1) }}%
                  </small>
                </div>
              </div>
              <div class="progress mb-3" style="height: 6px;">
                <div class="progress-bar bg-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}"
                     style="width: {{ min(abs($pengeluaranGrowth), 100) }}%"></div>
              </div>
            </div>
          </div>
          <hr>
          <div class="text-center">
            <h6 class="mb-1">Net Growth</h6>
            @php
              $netGrowth = $pendapatanGrowth - $pengeluaranGrowth;
            @endphp
            <h4 class="text-{{ $netGrowth >= 0 ? 'success' : 'danger' }}">
              @if($netGrowth >= 0) +@endif{{ number_format($netGrowth, 1) }}%
            </h4>
            <small class="text-muted">Pertumbuhan bersih</small>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">Breakdown Kategori Pengeluaran</h6>
        </div>
        <div class="card-body">
          <div class="row">
            @php
              $categories = [
                ['name' => 'Operasional', 'amount' => $totalPengeluaran * 0.6, 'color' => 'primary'],
                ['name' => 'Marketing', 'amount' => $totalPengeluaran * 0.2, 'color' => 'info'],
                ['name' => 'Administrasi', 'amount' => $totalPengeluaran * 0.15, 'color' => 'warning'],
                ['name' => 'Lainnya', 'amount' => $totalPengeluaran * 0.05, 'color' => 'secondary']
              ];
            @endphp
            @foreach($categories as $category)
            <div class="col-12 mb-3">
              <div class="d-flex justify-content-between align-items-center mb-1">
                <span class="fw-medium">{{ $category['name'] }}</span>
                <span class="text-muted">Rp {{ number_format($category['amount'], 0, ',', '.') }}</span>
              </div>
              <div class="progress" style="height: 6px;">
                <div class="progress-bar bg-{{ $category['color'] }}"
                     style="width: {{ ($category['amount'] / $totalPengeluaran) * 100 }}%"></div>
              </div>
              <small class="text-muted">{{ number_format(($category['amount'] / $totalPengeluaran) * 100, 1) }}% dari total</small>
            </div>
            @endforeach
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Advanced Charts Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0"><i class="bx bx-bar-chart-alt-2 me-2"></i>Advanced Data Visualizations</h5>
          <div class="d-flex gap-2">
            <select class="form-select form-select-sm" id="chartTypeSelector" onchange="changeChartType()">
              <option value="line">Line Chart</option>
              <option value="bar">Bar Chart</option>
              <option value="area">Area Chart</option>
              <option value="combo">Combination</option>
              <option value="pie">Pie Chart</option>
              <option value="donut">Donut Chart</option>
              <option value="scatter">Scatter Plot</option>
              <option value="heatmap">Heatmap</option>
            </select>
            <button class="btn btn-sm btn-outline-primary" onclick="toggleFullscreen('mainChartContainer')">
              <i class="bx bx-fullscreen"></i>
            </button>
            <div class="dropdown">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bx bx-dots-vertical-rounded"></i>
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportChart('chartLaporan')">Export PNG</a></li>
                <li><a class="dropdown-item" href="#" onclick="exportChartData()">Export Data</a></li>
                <li><a class="dropdown-item" href="#" onclick="shareChart()">Share Chart</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="resetChartZoom()">Reset Zoom</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body" id="mainChartContainer">
          <div class="row">
            <div class="col-12">
              <canvas id="chartLaporan" height="400"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Multi-Chart Dashboard -->
  <div class="row mb-4">
    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">Revenue Distribution</h6>
          <button class="btn btn-sm btn-outline-primary" onclick="toggleChartAnimation('pieChart')">
            <i class="bx bx-play"></i>
          </button>
        </div>
        <div class="card-body">
          <canvas id="pieChart" height="300"></canvas>
        </div>
      </div>
    </div>

    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">Monthly Performance Heatmap</h6>
          <div class="d-flex gap-1">
            <button class="btn btn-sm btn-outline-info" onclick="updateHeatmapData('revenue')">Revenue</button>
            <button class="btn btn-sm btn-outline-warning" onclick="updateHeatmapData('expenses')">Expenses</button>
            <button class="btn btn-sm btn-outline-success" onclick="updateHeatmapData('profit')">Profit</button>
          </div>
        </div>
        <div class="card-body">
          <canvas id="heatmapChart" height="300"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Interactive Charts Row -->
  <div class="row mb-4">
    <div class="col-xl-4 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">Expense Breakdown</h6>
        </div>
        <div class="card-body">
          <canvas id="donutChart" height="250"></canvas>
          <div class="mt-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="d-flex align-items-center">
                <span class="badge bg-primary me-2" style="width: 12px; height: 12px;"></span>
                Operasional
              </span>
              <span class="fw-bold">60%</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="d-flex align-items-center">
                <span class="badge bg-info me-2" style="width: 12px; height: 12px;"></span>
                Marketing
              </span>
              <span class="fw-bold">20%</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="d-flex align-items-center">
                <span class="badge bg-warning me-2" style="width: 12px; height: 12px;"></span>
                Administrasi
              </span>
              <span class="fw-bold">15%</span>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <span class="d-flex align-items-center">
                <span class="badge bg-secondary me-2" style="width: 12px; height: 12px;"></span>
                Lainnya
              </span>
              <span class="fw-bold">5%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-8 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">Scatter Plot: Revenue vs Expenses</h6>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="showTrendLine" onchange="toggleTrendLine()">
            <label class="form-check-label" for="showTrendLine">
              Trend Line
            </label>
          </div>
        </div>
        <div class="card-body">
          <canvas id="scatterChart" height="250"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Monthly Report -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0"><i class="bx bx-table me-2"></i>Laporan Bulanan Detail</h5>
          <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" onclick="exportTableToExcel()">
              <i class="bx bx-download me-1"></i>Export Excel
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="printTable()">
              <i class="bx bx-printer me-1"></i>Print
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover" id="monthlyDetailTable">
              <thead class="table-dark">
                <tr>
                  <th rowspan="2" class="align-middle">Bulan</th>
                  <th colspan="2" class="text-center">Pendapatan</th>
                  <th colspan="2" class="text-center">Pengeluaran</th>
                  <th rowspan="2" class="text-center align-middle">Laba/Rugi</th>
                  <th rowspan="2" class="text-center align-middle">Margin (%)</th>
                  <th rowspan="2" class="text-center align-middle">Trend</th>
                  <th rowspan="2" class="text-center align-middle">Status</th>
                </tr>
                <tr>
                  <th class="text-center">Jumlah</th>
                  <th class="text-center">Growth</th>
                  <th class="text-center">Jumlah</th>
                  <th class="text-center">Growth</th>
                </tr>
              </thead>
              <tbody>
                @foreach($laporan as $index => $item)
                @php
                  $prevItem = $index > 0 ? $laporan[$index - 1] : null;
                  $pendapatanGrowth = ($prevItem && $prevItem['pendapatan'] > 0) ? (($item['pendapatan'] - $prevItem['pendapatan']) / $prevItem['pendapatan']) * 100 : 0;
                  $pengeluaranGrowth = ($prevItem && $prevItem['pengeluaran'] > 0) ? (($item['pengeluaran'] - $prevItem['pengeluaran']) / $prevItem['pengeluaran']) * 100 : 0;
                  $margin = $item['pendapatan'] > 0 ? ($item['laba_rugi'] / $item['pendapatan']) * 100 : 0;
                @endphp
                <tr>
                  <td class="fw-bold">{{ $item['bulan'] }}</td>
                  <td class="text-end">
                    <span class="text-success fw-medium">Rp {{ number_format($item['pendapatan'], 0, ',', '.') }}</span>
                  </td>
                  <td class="text-center">
                    @if($prevItem)
                      <span class="badge bg-{{ $pendapatanGrowth >= 0 ? 'success' : 'danger' }}">
                        {{ $pendapatanGrowth >= 0 ? '+' : '' }}{{ number_format($pendapatanGrowth, 1) }}%
                      </span>
                    @else
                      <span class="text-muted">-</span>
                    @endif
                  </td>
                  <td class="text-end">
                    <span class="text-danger fw-medium">Rp {{ number_format($item['pengeluaran'], 0, ',', '.') }}</span>
                  </td>
                  <td class="text-center">
                    @if($prevItem)
                      <span class="badge bg-{{ $pengeluaranGrowth <= 0 ? 'success' : 'danger' }}">
                        {{ $pengeluaranGrowth >= 0 ? '+' : '' }}{{ number_format($pengeluaranGrowth, 1) }}%
                      </span>
                    @else
                      <span class="text-muted">-</span>
                    @endif
                  </td>
                  <td class="text-end">
                    <span class="fw-bold text-{{ $item['laba_rugi'] >= 0 ? 'success' : 'danger' }}">
                      Rp {{ number_format($item['laba_rugi'], 0, ',', '.') }}
                    </span>
                  </td>
                  <td class="text-center">
                    <span class="badge bg-{{ $margin >= 20 ? 'success' : ($margin >= 10 ? 'warning' : 'danger') }}">
                      {{ number_format($margin, 1) }}%
                    </span>
                  </td>
                  <td class="text-center">
                    @if($item['laba_rugi'] > 0)
                      <i class="bx bx-trending-up text-success fs-5"></i>
                    @elseif($item['laba_rugi'] < 0)
                      <i class="bx bx-trending-down text-danger fs-5"></i>
                    @else
                      <i class="bx bx-minus text-muted fs-5"></i>
                    @endif
                  </td>
                  <td class="text-center">
                    @if($margin >= 15)
                      <span class="badge bg-success">Excellent</span>
                    @elseif($margin >= 10)
                      <span class="badge bg-warning">Good</span>
                    @elseif($margin >= 5)
                      <span class="badge bg-info">Fair</span>
                    @else
                      <span class="badge bg-danger">Poor</span>
                    @endif
                  </td>
                </tr>
                @endforeach
              </tbody>
              <tfoot class="table-light">
                <tr class="fw-bold">
                  <td>TOTAL</td>
                  <td class="text-end text-success">Rp {{ number_format($totalPendapatan, 0, ',', '.') }}</td>
                  <td class="text-center">
                    <span class="badge bg-primary">{{ number_format($pendapatanGrowth, 1) }}%</span>
                  </td>
                  <td class="text-end text-danger">Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}</td>
                  <td class="text-center">
                    <span class="badge bg-primary">{{ number_format($pengeluaranGrowth, 1) }}%</span>
                  </td>
                  <td class="text-end text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }}">
                    Rp {{ number_format($totalLabaRugi, 0, ',', '.') }}
                  </td>
                  <td class="text-center">
                    <span class="badge bg-primary">{{ number_format($profitMargin, 1) }}%</span>
                  </td>
                  <td class="text-center">-</td>
                  <td class="text-center">
                    @if($profitMargin >= 15)
                      <span class="badge bg-success">Excellent</span>
                    @elseif($profitMargin >= 10)
                      <span class="badge bg-warning">Good</span>
                    @else
                      <span class="badge bg-danger">Needs Improvement</span>
                    @endif
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
    </div>
  </div>

  <!-- Predictive Analytics Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="bx bx-crystal-ball me-2"></i>Analisis Prediktif & Forecasting
          </h5>
          <div class="d-flex gap-2">
            <select class="form-select form-select-sm" id="forecastPeriod" style="width: auto;">
              <option value="3">3 Bulan</option>
              <option value="6" selected>6 Bulan</option>
              <option value="12">12 Bulan</option>
            </select>
            <button class="btn btn-sm btn-primary" onclick="generatePredictions()">
              <i class="bx bx-refresh me-1"></i>Generate
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <!-- Prediction Charts -->
            <div class="col-xl-8 col-12 mb-4">
              <div class="row">
                <div class="col-12 mb-3">
                  <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Revenue Forecast</h6>
                    <span class="badge bg-info" id="confidenceLevel">Confidence: 85%</span>
                  </div>
                  <canvas id="predictionChart" height="300"></canvas>
                </div>
              </div>

              <!-- Trend Analysis -->
              <div class="row">
                <div class="col-md-4 mb-3">
                  <div class="card bg-light h-100">
                    <div class="card-body text-center">
                      <i class="bx bx-trending-up text-success fs-1 mb-2"></i>
                      <h6>Revenue Trend</h6>
                      <span class="badge bg-success" id="revenueTrend">Increasing</span>
                      <small class="d-block text-muted mt-1" id="revenueTrendDetail">+12.5% growth</small>
                    </div>
                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card bg-light h-100">
                    <div class="card-body text-center">
                      <i class="bx bx-trending-down text-warning fs-1 mb-2"></i>
                      <h6>Expense Trend</h6>
                      <span class="badge bg-warning" id="expenseTrend">Stable</span>
                      <small class="d-block text-muted mt-1" id="expenseTrendDetail">+2.1% growth</small>
                    </div>
                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <div class="card bg-light h-100">
                    <div class="card-body text-center">
                      <i class="bx bx-pulse text-info fs-1 mb-2"></i>
                      <h6>Volatility</h6>
                      <span class="badge bg-info" id="volatilityLevel">Low</span>
                      <small class="d-block text-muted mt-1" id="volatilityDetail">15% variance</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Predictions Summary -->
            <div class="col-xl-4 col-12 mb-4">
              <div class="card h-100">
                <div class="card-header">
                  <h6 class="card-title mb-0">Forecast Summary</h6>
                </div>
                <div class="card-body">
                  <div id="predictionSummary">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                      <span>Next Month Revenue:</span>
                      <span class="fw-bold text-success" id="nextMonthRevenue">Rp 0</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                      <span>Next Month Expenses:</span>
                      <span class="fw-bold text-danger" id="nextMonthExpenses">Rp 0</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                      <span>Predicted Profit:</span>
                      <span class="fw-bold text-info" id="nextMonthProfit">Rp 0</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                      <span>6-Month Total Revenue:</span>
                      <span class="fw-bold" id="sixMonthRevenue">Rp 0</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                      <span>Expected Growth:</span>
                      <span class="fw-bold text-success" id="expectedGrowth">+0%</span>
                    </div>
                  </div>

                  <div class="mt-4">
                    <h6 class="text-primary mb-2">Risk Assessment</h6>
                    <div class="progress mb-2" style="height: 8px;">
                      <div class="progress-bar bg-success" id="riskBar" style="width: 70%"></div>
                    </div>
                    <small class="text-muted" id="riskAssessment">Low Risk - Stable growth expected</small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI Recommendations -->
          <div class="row">
            <div class="col-12">
              <div class="card bg-gradient-primary text-white">
                <div class="card-header">
                  <h6 class="card-title mb-0 text-white">
                    <i class="bx bx-brain me-2"></i>AI-Powered Recommendations
                  </h6>
                </div>
                <div class="card-body">
                  <div id="aiRecommendations" class="row">
                    <!-- Recommendations will be loaded here -->
                    <div class="col-md-4 mb-3">
                      <div class="d-flex align-items-start">
                        <div class="avatar avatar-sm me-3">
                          <div class="avatar-initial bg-white text-primary rounded">
                            <i class="bx bx-trending-up"></i>
                          </div>
                        </div>
                        <div>
                          <h6 class="text-white mb-1">Revenue Optimization</h6>
                          <small class="opacity-75">Focus on high-margin services to maximize revenue growth</small>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="d-flex align-items-start">
                        <div class="avatar avatar-sm me-3">
                          <div class="avatar-initial bg-white text-primary rounded">
                            <i class="bx bx-shield-check"></i>
                          </div>
                        </div>
                        <div>
                          <h6 class="text-white mb-1">Risk Mitigation</h6>
                          <small class="opacity-75">Diversify revenue streams to reduce volatility</small>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="d-flex align-items-start">
                        <div class="avatar avatar-sm me-3">
                          <div class="avatar-initial bg-white text-primary rounded">
                            <i class="bx bx-cog"></i>
                          </div>
                        </div>
                        <div>
                          <h6 class="text-white mb-1">Cost Efficiency</h6>
                          <small class="opacity-75">Optimize operational costs for better margins</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Executive Summary Footer -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card bg-light">
        <div class="card-header">
          <h5 class="card-title mb-0"><i class="bx bx-file-blank me-2"></i>Ringkasan Eksekutif</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-8">
              <h6 class="text-primary">Kesimpulan Kinerja Tahun {{ $tahun }}:</h6>
              <ul class="list-unstyled">
                <li class="mb-2">
                  <i class="bx bx-check-circle text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }} me-2"></i>
                  <strong>Status Keuangan:</strong>
                  {{ $totalLabaRugi >= 0 ? 'Profitable' : 'Loss' }} dengan
                  {{ $totalLabaRugi >= 0 ? 'laba' : 'rugi' }} sebesar
                  <span class="text-{{ $totalLabaRugi >= 0 ? 'success' : 'danger' }}">
                    Rp {{ number_format(abs($totalLabaRugi), 0, ',', '.') }}
                  </span>
                </li>
                <li class="mb-2">
                  <i class="bx bx-trending-{{ $profitMargin >= 15 ? 'up text-success' : ($profitMargin >= 5 ? 'up text-warning' : 'down text-danger') }} me-2"></i>
                  <strong>Margin Keuntungan:</strong> {{ number_format($profitMargin, 1) }}%
                  @if($profitMargin >= 15)
                    (Sangat Baik)
                  @elseif($profitMargin >= 10)
                    (Baik)
                  @elseif($profitMargin >= 5)
                    (Cukup)
                  @else
                    (Perlu Perbaikan)
                  @endif
                </li>
                <li class="mb-2">
                  <i class="bx bx-bar-chart text-info me-2"></i>
                  <strong>Efisiensi Operasional:</strong> {{ number_format($efficiencyRatio, 1) }}%
                  ({{ $efficiencyRatio <= 70 ? 'Sangat Efisien' : ($efficiencyRatio <= 85 ? 'Efisien' : 'Perlu Optimasi') }})
                </li>
                <li class="mb-2">
                  <i class="bx bx-wallet text-primary me-2"></i>
                  <strong>Posisi Kas:</strong> Rp {{ number_format($kasBesar + $kasKecil, 0, ',', '.') }}
                  ({{ $kasBesar + $kasKecil >= $totalPengeluaran * 0.1 ? 'Likuiditas Sehat' : 'Perlu Perhatian' }})
                </li>
              </ul>
            </div>
            <div class="col-md-4">
              <h6 class="text-warning">Rekomendasi Strategis:</h6>
              <div class="alert alert-info p-3">
                @if($totalLabaRugi >= 0 && $profitMargin >= 15)
                  <strong>Pertahankan Momentum:</strong><br>
                  • Ekspansi pasar yang terukur<br>
                  • Investasi teknologi<br>
                  • Diversifikasi produk/layanan
                @elseif($totalLabaRugi >= 0 && $profitMargin < 15)
                  <strong>Optimasi Efisiensi:</strong><br>
                  • Review struktur biaya<br>
                  • Tingkatkan produktivitas<br>
                  • Fokus pada margin tinggi
                @else
                  <strong>Perbaikan Mendesak:</strong><br>
                  • Audit biaya operasional<br>
                  • Strategi peningkatan revenue<br>
                  • Restrukturisasi jika perlu
                @endif
              </div>
            </div>
          </div>
          <hr>
          <div class="row text-center">
            <div class="col-md-3">
              <small class="text-muted">Laporan dibuat:</small><br>
              <strong>{{ now()->format('d M Y, H:i') }} WIB</strong>
            </div>
            <div class="col-md-3">
              <small class="text-muted">Periode analisis:</small><br>
              <strong>12 Bulan ({{ $tahun }})</strong>
            </div>
            <div class="col-md-3">
              <small class="text-muted">Total transaksi:</small><br>
              <strong>{{ number_format($totalTransaksi ?? 0) }}</strong>
            </div>
            <div class="col-md-3">
              <small class="text-muted">Akurasi data:</small><br>
              <strong class="text-success">99.9%</strong>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">📋 RAB (Rencana Anggaran Biaya)</h5>
      <table class="table table-bordered table-sm">
        <thead class="table-light">
          <tr>
            <th>Nama</th>
            <th>Anggaran</th>
            <th>Realisasi</th>
            <th>Sisa</th>
          </tr>
        </thead>
        <tbody>
          @forelse ($rabData as $rab)
          <tr>
            <td>{{ $rab['nama'] }}</td>
            <td>Rp {{ number_format($rab['anggaran'], 0, ',', '.') }}</td>
            <td>Rp {{ number_format($rab['realisasi'], 0, ',', '.') }}</td>
            <td>Rp {{ number_format($rab['sisa'], 0, ',', '.') }}</td>
          </tr>
          @empty
          <tr>
            <td colspan="4" class="text-center">Tidak ada data RAB</td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
  </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
const ctx = document.getElementById('chartLaporan').getContext('2d');
new Chart(ctx, {
    type: 'bar',
    data: {
        labels: {!! json_encode(array_column($laporan, 'bulan')) !!},
        datasets: [
            {
                label: 'Pendapatan',
                data: {!! json_encode(array_column($laporan, 'pendapatan')) !!},
                backgroundColor: 'rgba(40, 167, 69, 0.6)',
            },
            {
                label: 'Pengeluaran',
                data: {!! json_encode(array_column($laporan, 'pengeluaran')) !!},
                backgroundColor: 'rgba(220, 53, 69, 0.6)',
            },
            {
                label: 'Laba / Rugi',
                data: {!! json_encode(array_column($laporan, 'laba_rugi')) !!},
                type: 'line',
                borderColor: 'rgba(0, 123, 255, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: false
            }
        ]
    },
    options: {
        responsive: true,
        scales: {
            y: { beginAtZero: true }
        }
    }
});

// Additional JavaScript Functions
function exportToExcel() {
    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws_data = [
        ['Laporan Keuangan Tahun {{ $tahun }}'],
        ['Diperbarui: ' + new Date().toLocaleDateString('id-ID')],
        [],
        ['Ringkasan Keuangan'],
        ['Total Pendapatan', 'Rp {{ number_format($totalPendapatan, 0, ",", ".") }}'],
        ['Total Pengeluaran', 'Rp {{ number_format($totalPengeluaran, 0, ",", ".") }}'],
        ['Laba/Rugi', 'Rp {{ number_format($totalLabaRugi, 0, ",", ".") }}'],
        ['Margin Keuntungan', '{{ number_format($profitMargin, 1) }}%'],
        []
    ];

    // Add monthly data
    ws_data.push(['Bulan', 'Pendapatan', 'Pengeluaran', 'Laba/Rugi', 'Margin']);
    @foreach($laporan as $item)
    ws_data.push([
        '{{ $item["bulan"] }}',
        {{ $item['pendapatan'] }},
        {{ $item['pengeluaran'] }},
        {{ $item['laba_rugi'] }},
        '{{ number_format($item["pendapatan"] > 0 ? ($item["laba_rugi"] / $item["pendapatan"]) * 100 : 0, 1) }}%'
    ]);
    @endforeach

    const ws = XLSX.utils.aoa_to_sheet(ws_data);
    XLSX.utils.book_append_sheet(wb, ws, 'Laporan Keuangan');
    XLSX.writeFile(wb, 'Laporan_Keuangan_{{ $tahun }}.xlsx');
}

function exportTableToExcel() {
    const table = document.getElementById('monthlyDetailTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: 'Detail Bulanan'});
    XLSX.writeFile(wb, 'Detail_Laporan_Bulanan_{{ $tahun }}.xlsx');
}

function printTable() {
    const printContent = document.getElementById('monthlyDetailTable').outerHTML;
    const printWindow = window.open('', '', 'height=600,width=800');
    printWindow.document.write(`
        <html>
        <head>
            <title>Laporan Keuangan {{ $tahun }}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .text-success { color: #28a745; }
                .text-danger { color: #dc3545; }
                .text-center { text-align: center; }
                .text-end { text-align: right; }
                .badge { padding: 2px 6px; border-radius: 3px; font-size: 10px; }
                .bg-success { background-color: #28a745; color: white; }
                .bg-danger { background-color: #dc3545; color: white; }
                .bg-warning { background-color: #ffc107; color: black; }
                .bg-info { background-color: #17a2b8; color: white; }
            </style>
        </head>
        <body>
            <h2>Laporan Keuangan Detail - Tahun {{ $tahun }}</h2>
            <p>Dicetak pada: ${new Date().toLocaleDateString('id-ID')}</p>
            ${printContent}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function toggleChartType() {
    // Toggle between line and bar chart
    const chart = Chart.getChart('chartLaporan');
    if (chart) {
        chart.config.type = chart.config.type === 'line' ? 'bar' : 'line';
        chart.update();
    }
}

function exportChart() {
    const canvas = document.getElementById('chartLaporan');
    const url = canvas.toDataURL('image/png');
    const a = document.createElement('a');
    a.href = url;
    a.download = 'Grafik_Keuangan_{{ $tahun }}.png';
    a.click();
}

// Advanced Filtering Functions
function toggleAdvancedFilters() {
    const advancedFilters = document.getElementById('advancedFilters');
    const isVisible = advancedFilters.style.display !== 'none';

    if (isVisible) {
        advancedFilters.style.display = 'none';
    } else {
        advancedFilters.style.display = 'block';
        // Animate the appearance
        advancedFilters.style.opacity = '0';
        setTimeout(() => {
            advancedFilters.style.transition = 'opacity 0.3s ease-in-out';
            advancedFilters.style.opacity = '1';
        }, 10);
    }
}

function resetFilters() {
    document.getElementById('filterForm').reset();
    document.getElementById('advancedFilters').style.display = 'none';

    // Show reset confirmation
    showNotification('Filter telah direset', 'info');
}

function applyFilters() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);

    // Show loading state
    showLoadingState(true);

    // Collect filter data
    const filters = {
        tahun: formData.get('tahun'),
        bulan_range: formData.get('bulan_range'),
        kategori: formData.get('kategori'),
        chart_type: formData.get('chart_type'),
        start_date: formData.get('start_date'),
        end_date: formData.get('end_date'),
        min_amount: formData.get('min_amount'),
        max_amount: formData.get('max_amount'),
        status_filter: formData.get('status_filter'),
        payment_method: formData.getAll('payment_method'),
        trend_analysis: formData.get('trend_analysis'),
        comparison: formData.get('comparison')
    };

    // Apply filters via AJAX
    applyFiltersAjax(filters);
}

function applyFiltersAjax(filters) {
    fetch('/laporan/filter', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDashboard(data);
            showNotification('Filter berhasil diterapkan', 'success');
        } else {
            showNotification('Gagal menerapkan filter', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Terjadi kesalahan', 'error');
    })
    .finally(() => {
        showLoadingState(false);
    });
}

function applyPreset(presetType) {
    const presets = {
        profitable: {
            status_filter: 'profit',
            trend_analysis: 'growth',
            comparison: 'yoy',
            kategori: 'profitabilitas'
        },
        high_expense: {
            kategori: 'pengeluaran',
            trend_analysis: 'growth',
            comparison: 'mom'
        },
        growth: {
            trend_analysis: 'growth',
            comparison: 'yoy',
            chart_type: 'line'
        },
        seasonal: {
            trend_analysis: 'seasonal',
            bulan_range: 'all',
            chart_type: 'area'
        },
        efficiency: {
            kategori: 'efficiency',
            trend_analysis: 'moving_avg',
            comparison: 'budget'
        }
    };

    const preset = presets[presetType];
    if (preset) {
        // Apply preset values to form
        Object.keys(preset).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = preset[key];
            }
        });

        // Show advanced filters if needed
        document.getElementById('advancedFilters').style.display = 'block';

        showNotification(`Preset "${presetType}" diterapkan`, 'info');
    }
}

function saveFilterPreset() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);

    const presetName = prompt('Nama preset:');
    if (presetName) {
        const preset = {};
        for (let [key, value] of formData.entries()) {
            preset[key] = value;
        }

        // Save to localStorage
        const savedPresets = JSON.parse(localStorage.getItem('filterPresets') || '{}');
        savedPresets[presetName] = preset;
        localStorage.setItem('filterPresets', JSON.stringify(savedPresets));

        showNotification(`Preset "${presetName}" disimpan`, 'success');
    }
}

function exportFilteredData() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);

    // Create export URL with current filters
    const params = new URLSearchParams(formData);
    const exportUrl = `/laporan/export?${params.toString()}`;

    // Open export in new window
    window.open(exportUrl, '_blank');

    showNotification('Export dimulai...', 'info');
}

function updateDashboard(data) {
    // Update KPI cards
    if (data.kpi) {
        updateKPICards(data.kpi);
    }

    // Update charts
    if (data.chartData) {
        updateCharts(data.chartData);
    }

    // Update tables
    if (data.tableData) {
        updateTables(data.tableData);
    }

    // Update last update time
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('id-ID');
    document.getElementById('dataPoints').textContent = data.totalRecords || '0';
}

function updateKPICards(kpiData) {
    // Update each KPI card with new data
    Object.keys(kpiData).forEach(key => {
        const element = document.querySelector(`[data-kpi="${key}"]`);
        if (element) {
            element.textContent = kpiData[key];
        }
    });
}

function updateCharts(chartData) {
    // Update existing charts with new data
    const chart = Chart.getChart('chartLaporan');
    if (chart && chartData.labels && chartData.datasets) {
        chart.data.labels = chartData.labels;
        chart.data.datasets = chartData.datasets;
        chart.update('active');
    }
}

function updateTables(tableData) {
    // Update table content
    const tableBody = document.querySelector('#monthlyDetailTable tbody');
    if (tableBody && tableData.rows) {
        tableBody.innerHTML = tableData.rows;
    }
}

function showLoadingState(show) {
    const loadingElements = document.querySelectorAll('.loading-overlay');
    loadingElements.forEach(el => {
        el.style.display = show ? 'block' : 'none';
    });

    // Disable/enable form elements
    const formElements = document.querySelectorAll('#filterForm input, #filterForm select, #filterForm button');
    formElements.forEach(el => {
        el.disabled = show;
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Real-time data updates
let realTimeInterval;
let miniChart;
let realtimeData = [];
let connectionRetries = 0;
const maxRetries = 5;

function startRealTimeUpdates() {
    const autoRefreshCheckbox = document.getElementById('autoRefresh');

    if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
        realTimeInterval = setInterval(() => {
            updateRealTimeData();
        }, 30000); // Update every 30 seconds

        // Show connection status
        updateConnectionStatus('connected');
    }
}

function stopRealTimeUpdates() {
    if (realTimeInterval) {
        clearInterval(realTimeInterval);
        realTimeInterval = null;
    }
    updateConnectionStatus('disconnected');
}

function updateRealTimeData() {
    const startTime = performance.now();

    // Show loading indicator
    showLoadingIndicator(true);

    fetch('/laporan/realtime-data', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Reset retry counter on success
            connectionRetries = 0;

            // Update real-time indicators
            updateConnectionStatus('connected');
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('id-ID');
            document.getElementById('updateIndicator').innerHTML = '<i class="bx bx-check-circle"></i>';
            document.getElementById('updateIndicator').className = 'badge bg-success ms-1';

            // Update metrics
            if (data.metrics) {
                updateRealTimeMetrics(data.metrics);
                updateLiveWidgets(data.metrics);

                // Add to trend data
                realtimeData.push({
                    time: new Date(),
                    revenue: data.metrics.totalPendapatan || 0,
                    transactions: data.metrics.activeTransactions || 0
                });

                // Keep only last 20 data points
                if (realtimeData.length > 20) {
                    realtimeData.shift();
                }

                // Update mini chart
                updateMiniChart();
            }

            // Show success notification occasionally
            if (Math.random() < 0.1) { // 10% chance
                showLiveNotification('Data updated successfully', 'success', 2000);
            }
        }
    })
    .catch(error => {
        console.error('Real-time update error:', error);
        connectionRetries++;

        updateConnectionStatus('error');
        document.getElementById('updateIndicator').innerHTML = '<i class="bx bx-x-circle"></i>';
        document.getElementById('updateIndicator').className = 'badge bg-danger ms-1';

        // Show error notification
        showLiveNotification(`Connection error (${connectionRetries}/${maxRetries})`, 'error', 3000);

        // Stop updates if too many retries
        if (connectionRetries >= maxRetries) {
            stopRealTimeUpdates();
            showLiveNotification('Real-time updates stopped due to connection issues', 'warning', 5000);
        }
    })
    .finally(() => {
        showLoadingIndicator(false);
    });
}

function updateRealTimeMetrics(metrics) {
    // Update real-time data attributes
    Object.keys(metrics).forEach(key => {
        const element = document.querySelector(`[data-realtime="${key}"]`);
        if (element) {
            const oldValue = element.textContent;
            const newValue = formatMetricValue(key, metrics[key]);

            if (oldValue !== newValue) {
                // Animate value change
                element.style.transition = 'all 0.3s ease';
                element.style.transform = 'scale(1.05)';
                element.textContent = newValue;

                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 300);

                // Show change indicator
                showValueChange(element, oldValue, newValue);
            }
        }
    });
}

function updateLiveWidgets(metrics) {
    // Update live revenue
    const liveRevenue = document.getElementById('liveRevenue');
    if (liveRevenue && metrics.totalPendapatan) {
        liveRevenue.textContent = 'Rp ' + new Intl.NumberFormat('id-ID').format(metrics.totalPendapatan);
    }

    // Update active transactions
    const activeTransactions = document.getElementById('activeTransactions');
    if (activeTransactions && metrics.activeTransactions !== undefined) {
        activeTransactions.textContent = metrics.activeTransactions;
    }

    // Update system load (simulated)
    const systemLoad = document.getElementById('systemLoad');
    if (systemLoad) {
        const load = Math.round(Math.random() * 30 + 10); // Simulate 10-40% load
        systemLoad.textContent = load + '%';
    }

    // Update average response time
    const avgResponse = document.getElementById('avgResponse');
    if (avgResponse) {
        const responseTime = Math.round(Math.random() * 100 + 50); // Simulate 50-150ms
        avgResponse.textContent = responseTime + 'ms';
    }
}

function updateConnectionStatus(status) {
    const connectionStatus = document.getElementById('connectionStatus');
    const systemStatus = document.getElementById('systemStatus');

    switch (status) {
        case 'connected':
            if (connectionStatus) {
                connectionStatus.innerHTML = '<i class="bx bx-wifi fs-4"></i><small class="d-block">Connected</small>';
            }
            if (systemStatus) {
                systemStatus.innerHTML = '<i class="bx bx-wifi me-1"></i>Real-time';
                systemStatus.className = 'badge bg-success ms-1';
            }
            break;

        case 'disconnected':
            if (connectionStatus) {
                connectionStatus.innerHTML = '<i class="bx bx-wifi-off fs-4"></i><small class="d-block">Disconnected</small>';
            }
            if (systemStatus) {
                systemStatus.innerHTML = '<i class="bx bx-wifi-off me-1"></i>Offline';
                systemStatus.className = 'badge bg-secondary ms-1';
            }
            break;

        case 'error':
            if (connectionStatus) {
                connectionStatus.innerHTML = '<i class="bx bx-error-circle fs-4"></i><small class="d-block">Error</small>';
            }
            if (systemStatus) {
                systemStatus.innerHTML = '<i class="bx bx-error-circle me-1"></i>Error';
                systemStatus.className = 'badge bg-danger ms-1';
            }
            break;
    }
}

function showLoadingIndicator(show) {
    const spinner = document.getElementById('loadingSpinner');
    const connectionStatus = document.getElementById('connectionStatus');

    if (spinner && connectionStatus) {
        if (show) {
            spinner.style.display = 'block';
            connectionStatus.style.display = 'none';
        } else {
            spinner.style.display = 'none';
            connectionStatus.style.display = 'block';
        }
    }
}

function showLiveNotification(message, type = 'info', duration = 3000) {
    const container = document.getElementById('liveNotifications');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show mb-2`;
    notification.style.cssText = 'animation: slideInRight 0.3s ease-out;';

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'x-circle' :
                 type === 'warning' ? 'error-circle' : 'info-circle';

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="bx bx-${icon} me-2"></i>
            <small>${message}</small>
            <button type="button" class="btn-close btn-close-sm ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;

    container.appendChild(notification);

    // Auto remove
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

function updateMiniChart() {
    if (!miniChart || realtimeData.length < 2) return;

    const canvas = document.getElementById('miniChart');
    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Get revenue data for trend
    const revenues = realtimeData.map(d => d.revenue);
    const maxRevenue = Math.max(...revenues);
    const minRevenue = Math.min(...revenues);
    const range = maxRevenue - minRevenue || 1;

    // Draw trend line
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.beginPath();

    realtimeData.forEach((point, index) => {
        const x = realtimeData.length > 1 ? (index / (realtimeData.length - 1)) * canvas.width : 0;
        const y = range > 0 ? canvas.height - ((point.revenue - minRevenue) / range) * canvas.height : canvas.height / 2;

        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });

    ctx.stroke();
}

function formatMetricValue(key, value) {
    switch (key) {
        case 'totalPendapatan':
        case 'totalPengeluaran':
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
        case 'activeTransactions':
            return value.toString();
        default:
            return value.toString();
    }
}

function showValueChange(element, oldValue, newValue) {
    // Create change indicator
    const changeIndicator = element.parentNode.querySelector('.value-change-indicator');
    if (changeIndicator) {
        changeIndicator.remove();
    }

    const indicator = document.createElement('span');
    indicator.className = 'value-change-indicator position-absolute';
    indicator.style.cssText = 'top: -10px; right: -10px; font-size: 10px; animation: fadeInOut 2s ease-out;';
    indicator.innerHTML = '<i class="bx bx-trending-up text-success"></i>';

    element.parentNode.style.position = 'relative';
    element.parentNode.appendChild(indicator);

    setTimeout(() => indicator.remove(), 2000);
}

// Predictive Analytics Functions
let predictionChart;

function generatePredictions() {
    const forecastPeriod = document.getElementById('forecastPeriod').value;
    const tahun = document.querySelector('[name="tahun"]').value;

    showLoadingState(true);

    fetch(`/laporan/predictive?tahun=${tahun}&months=${forecastPeriod}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updatePredictionChart(data.predictions);
                updatePredictionSummary(data.predictions);
                updateAIRecommendations(data.predictions.recommendations);
                updateConfidenceLevel(data.confidence);

                showNotification('Predictions generated successfully', 'success');
            } else {
                showNotification('Failed to generate predictions', 'error');
            }
        })
        .catch(error => {
            console.error('Prediction error:', error);
            showNotification('Error generating predictions', 'error');
        })
        .finally(() => {
            showLoadingState(false);
        });
}

function updatePredictionChart(predictions) {
    const ctx = document.getElementById('predictionChart');
    if (!ctx) return;

    // Destroy existing chart
    if (predictionChart) {
        predictionChart.destroy();
    }

    // Prepare data
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
                   'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];

    const revenueLabels = predictions.revenue.map(item =>
        months[item.month - 1] + ' ' + item.year
    );

    const revenueData = predictions.revenue.map(item => item.predicted_revenue);
    const expenseData = predictions.expenses.map(item => item.predicted_expenses);
    const cashFlowData = predictions.cashFlow.map(item => item.predicted_cashflow);

    // Create chart
    predictionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: revenueLabels,
            datasets: [{
                label: 'Predicted Revenue',
                data: revenueData,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                borderDash: [5, 5] // Dashed line for predictions
            }, {
                label: 'Predicted Expenses',
                data: expenseData,
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                borderDash: [5, 5]
            }, {
                label: 'Predicted Cash Flow',
                data: cashFlowData,
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4,
                borderDash: [10, 5]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            const confidence = predictions.revenue[context.dataIndex]?.confidence || 0;
                            return context.dataset.label + ': Rp ' +
                                   new Intl.NumberFormat('id-ID').format(context.parsed.y) +
                                   ` (${Math.round(confidence * 100)}% confidence)`;
                        }
                    }
                },
                annotation: {
                    annotations: {
                        line1: {
                            type: 'line',
                            xMin: 0,
                            xMax: revenueLabels.length,
                            borderColor: 'rgba(255, 99, 132, 0.5)',
                            borderWidth: 2,
                            borderDash: [2, 2],
                            label: {
                                content: 'Prediction Start',
                                enabled: true,
                                position: 'start'
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Period'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Amount (Rp)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}

function updatePredictionSummary(predictions) {
    // Update next month predictions
    if (predictions.revenue.length > 0) {
        const nextRevenue = predictions.revenue[0].predicted_revenue;
        const nextExpenses = predictions.expenses[0].predicted_expenses;
        const nextProfit = nextRevenue - nextExpenses;

        document.getElementById('nextMonthRevenue').textContent =
            'Rp ' + new Intl.NumberFormat('id-ID').format(nextRevenue);
        document.getElementById('nextMonthExpenses').textContent =
            'Rp ' + new Intl.NumberFormat('id-ID').format(nextExpenses);
        document.getElementById('nextMonthProfit').textContent =
            'Rp ' + new Intl.NumberFormat('id-ID').format(nextProfit);
    }

    // Update 6-month totals
    const sixMonthRevenue = predictions.revenue.slice(0, 6).reduce((sum, item) =>
        sum + item.predicted_revenue, 0);
    document.getElementById('sixMonthRevenue').textContent =
        'Rp ' + new Intl.NumberFormat('id-ID').format(sixMonthRevenue);

    // Update growth rate
    if (predictions.trends && predictions.trends.revenue_trend) {
        const growthDirection = predictions.trends.revenue_trend.direction;
        const growthStrength = predictions.trends.revenue_trend.strength;
        const growthPercent = Math.round(growthStrength * 100);

        document.getElementById('expectedGrowth').textContent =
            (growthDirection === 'increasing' ? '+' : growthDirection === 'decreasing' ? '-' : '') +
            growthPercent + '%';
        document.getElementById('expectedGrowth').className =
            'fw-bold text-' + (growthDirection === 'increasing' ? 'success' :
                              growthDirection === 'decreasing' ? 'danger' : 'muted');
    }

    // Update trend indicators
    updateTrendIndicators(predictions.trends);

    // Update risk assessment
    updateRiskAssessment(predictions);
}

function updateTrendIndicators(trends) {
    if (!trends) return;

    // Revenue trend
    const revenueTrend = trends.revenue_trend;
    if (revenueTrend) {
        const trendElement = document.getElementById('revenueTrend');
        const detailElement = document.getElementById('revenueTrendDetail');

        trendElement.textContent = revenueTrend.direction.charAt(0).toUpperCase() +
                                  revenueTrend.direction.slice(1);
        trendElement.className = 'badge bg-' +
            (revenueTrend.direction === 'increasing' ? 'success' :
             revenueTrend.direction === 'decreasing' ? 'danger' : 'secondary');

        if (trends.growth_rate && trends.growth_rate.revenue) {
            detailElement.textContent =
                (trends.growth_rate.revenue >= 0 ? '+' : '') +
                trends.growth_rate.revenue.toFixed(1) + '% growth';
        }
    }

    // Expense trend
    const expenseTrend = trends.expense_trend;
    if (expenseTrend) {
        const trendElement = document.getElementById('expenseTrend');
        const detailElement = document.getElementById('expenseTrendDetail');

        trendElement.textContent = expenseTrend.direction.charAt(0).toUpperCase() +
                                  expenseTrend.direction.slice(1);
        trendElement.className = 'badge bg-' +
            (expenseTrend.direction === 'decreasing' ? 'success' :
             expenseTrend.direction === 'increasing' ? 'warning' : 'secondary');

        if (trends.growth_rate && trends.growth_rate.expenses) {
            detailElement.textContent =
                (trends.growth_rate.expenses >= 0 ? '+' : '') +
                trends.growth_rate.expenses.toFixed(1) + '% growth';
        }
    }

    // Volatility
    if (trends.volatility && trends.volatility.revenue) {
        const volatility = trends.volatility.revenue;
        const volatilityElement = document.getElementById('volatilityLevel');
        const detailElement = document.getElementById('volatilityDetail');

        const volatilityLevel = volatility < 0.2 ? 'Low' :
                               volatility < 0.4 ? 'Medium' : 'High';
        const volatilityColor = volatility < 0.2 ? 'success' :
                               volatility < 0.4 ? 'warning' : 'danger';

        volatilityElement.textContent = volatilityLevel;
        volatilityElement.className = 'badge bg-' + volatilityColor;
        detailElement.textContent = Math.round(volatility * 100) + '% variance';
    }
}

function updateRiskAssessment(predictions) {
    const riskBar = document.getElementById('riskBar');
    const riskAssessment = document.getElementById('riskAssessment');

    // Calculate risk based on volatility and trend
    let riskScore = 70; // Default medium-low risk

    if (predictions.trends) {
        const volatility = predictions.trends.volatility?.revenue || 0;
        const revenueTrend = predictions.trends.revenue_trend?.direction;

        // Adjust risk based on volatility
        if (volatility > 0.4) riskScore -= 30;
        else if (volatility > 0.2) riskScore -= 15;

        // Adjust risk based on trend
        if (revenueTrend === 'decreasing') riskScore -= 20;
        else if (revenueTrend === 'increasing') riskScore += 10;
    }

    riskScore = Math.max(10, Math.min(90, riskScore));

    const riskLevel = riskScore > 70 ? 'Low Risk' :
                     riskScore > 40 ? 'Medium Risk' : 'High Risk';
    const riskColor = riskScore > 70 ? 'success' :
                     riskScore > 40 ? 'warning' : 'danger';
    const riskDescription = riskScore > 70 ? 'Stable growth expected' :
                           riskScore > 40 ? 'Monitor trends closely' : 'Immediate action required';

    riskBar.style.width = riskScore + '%';
    riskBar.className = 'progress-bar bg-' + riskColor;
    riskAssessment.textContent = riskLevel + ' - ' + riskDescription;
}

function updateAIRecommendations(recommendations) {
    const container = document.getElementById('aiRecommendations');
    if (!container || !recommendations) return;

    container.innerHTML = '';

    recommendations.forEach((rec, index) => {
        const priorityColor = rec.priority === 'high' ? 'danger' :
                             rec.priority === 'medium' ? 'warning' : 'info';
        const icon = rec.type === 'revenue' ? 'trending-up' :
                    rec.type === 'expenses' ? 'trending-down' :
                    rec.type === 'stability' ? 'shield-check' :
                    rec.type === 'growth' ? 'line-chart' : 'cog';

        const recElement = document.createElement('div');
        recElement.className = 'col-md-4 mb-3';
        recElement.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="avatar avatar-sm me-3">
                    <div class="avatar-initial bg-white text-primary rounded">
                        <i class="bx bx-${icon}"></i>
                    </div>
                </div>
                <div>
                    <h6 class="text-white mb-1">
                        ${rec.title}
                        <span class="badge bg-${priorityColor} ms-1">${rec.priority}</span>
                    </h6>
                    <small class="opacity-75">${rec.description}</small>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-light" onclick="showRecommendationDetails(${index})">
                            <i class="bx bx-info-circle me-1"></i>Details
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.appendChild(recElement);
    });
}

function updateConfidenceLevel(confidence) {
    const confidenceElement = document.getElementById('confidenceLevel');
    if (confidenceElement && confidence) {
        confidenceElement.textContent = `Confidence: ${confidence.overall}%`;

        const confidenceColor = confidence.overall > 80 ? 'success' :
                                confidence.overall > 60 ? 'warning' : 'danger';
        confidenceElement.className = 'badge bg-' + confidenceColor;
    }
}

function showRecommendationDetails(index) {
    // This would show a modal with detailed recommendation information
    showNotification('Recommendation details feature coming soon', 'info');
}

// Advanced Chart Functions
let pieChart, donutChart, scatterChart, heatmapChart;
let currentChartType = 'line';

function initializeAdvancedCharts() {
    initializePieChart();
    initializeDonutChart();
    initializeScatterChart();
    initializeHeatmapChart();
}

function initializePieChart() {
    const ctx = document.getElementById('pieChart');
    if (!ctx) return;

    const data = {
        labels: ['Pendapatan Langganan', 'Pendapatan Non-Langganan', 'Pendapatan Lainnya'],
        datasets: [{
            data: [70, 25, 5],
            backgroundColor: [
                '#007bff',
                '#17a2b8',
                '#6c757d'
            ],
            borderWidth: 2,
            borderColor: '#fff',
            hoverBorderWidth: 4,
            hoverBorderColor: '#fff'
        }]
    };

    pieChart = new Chart(ctx, {
        type: 'pie',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                            return `${label}: ${percentage}%`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000
            }
        }
    });
}

function initializeDonutChart() {
    const ctx = document.getElementById('donutChart');
    if (!ctx) return;

    const data = {
        labels: ['Operasional', 'Marketing', 'Administrasi', 'Lainnya'],
        datasets: [{
            data: [60, 20, 15, 5],
            backgroundColor: [
                '#007bff',
                '#17a2b8',
                '#ffc107',
                '#6c757d'
            ],
            borderWidth: 0,
            cutout: '70%'
        }]
    };

    donutChart = new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            return `${label}: ${value}%`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 1500
            }
        }
    });
}

function initializeScatterChart() {
    const ctx = document.getElementById('scatterChart');
    if (!ctx) return;

    // Generate sample data points
    const scatterData = [];
    for (let i = 0; i < 12; i++) {
        const revenue = Math.random() * 50000000 + 10000000;
        const expenses = revenue * (0.6 + Math.random() * 0.3);
        scatterData.push({
            x: revenue,
            y: expenses,
            month: i + 1
        });
    }

    scatterChart = new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: 'Monthly Data',
                data: scatterData,
                backgroundColor: 'rgba(0, 123, 255, 0.6)',
                borderColor: '#007bff',
                borderWidth: 2,
                pointRadius: 8,
                pointHoverRadius: 12
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            const point = context[0];
                            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
                                              'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];
                            return `Bulan ${monthNames[point.raw.month - 1]}`;
                        },
                        label: function(context) {
                            const x = new Intl.NumberFormat('id-ID').format(context.parsed.x);
                            const y = new Intl.NumberFormat('id-ID').format(context.parsed.y);
                            return [`Pendapatan: Rp ${x}`, `Pengeluaran: Rp ${y}`];
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'linear',
                    position: 'bottom',
                    title: {
                        display: true,
                        text: 'Pendapatan (Rp)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value);
                        }
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Pengeluaran (Rp)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value);
                        }
                    }
                }
            }
        }
    });
}

function initializeHeatmapChart() {
    const ctx = document.getElementById('heatmapChart');
    if (!ctx) return;

    // Create heatmap data (simplified version using bar chart)
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
                   'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];
    const heatmapData = months.map((month, index) => ({
        x: month,
        y: Math.random() * 100 + 50 // Performance score
    }));

    heatmapChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: 'Performance Score',
                data: heatmapData.map(d => d.y),
                backgroundColor: function(context) {
                    const value = context.parsed.y;
                    const alpha = value / 150; // Normalize to 0-1
                    return `rgba(40, 167, 69, ${alpha})`;
                },
                borderColor: '#28a745',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Score: ${context.parsed.y.toFixed(1)}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 150,
                    title: {
                        display: true,
                        text: 'Performance Score'
                    }
                }
            }
        }
    });
}

function changeChartType() {
    const selectedType = document.getElementById('chartTypeSelector').value;
    const mainChart = Chart.getChart('chartLaporan');

    if (!mainChart) return;

    currentChartType = selectedType;

    switch (selectedType) {
        case 'line':
            updateMainChart('line', false);
            break;
        case 'bar':
            updateMainChart('bar', false);
            break;
        case 'area':
            updateMainChart('line', true);
            break;
        case 'combo':
            updateMainChart('combo', false);
            break;
        case 'pie':
            updateMainChart('pie', false);
            break;
        case 'donut':
            updateMainChart('doughnut', false);
            break;
        case 'scatter':
            updateMainChart('scatter', false);
            break;
        case 'heatmap':
            updateHeatmapView();
            break;
    }

    showNotification(`Chart changed to ${selectedType}`, 'success', 2000);
}

function updateMainChart(type, filled) {
    const chart = Chart.getChart('chartLaporan');
    if (!chart) return;

    // Update chart type
    chart.config.type = type;

    // Update dataset properties based on type
    chart.data.datasets.forEach((dataset, index) => {
        if (type === 'line' || type === 'area') {
            dataset.fill = filled;
            dataset.tension = 0.4;
            dataset.borderWidth = 3;
            dataset.pointRadius = 4;
            dataset.pointHoverRadius = 8;
        } else if (type === 'bar') {
            dataset.fill = false;
            dataset.borderWidth = 1;
            dataset.borderRadius = 4;
        } else if (type === 'pie' || type === 'doughnut') {
            // Convert line data to pie data
            const total = dataset.data.reduce((a, b) => a + b, 0);
            dataset.data = total > 0 ? dataset.data.map(value => (value / total) * 100) : dataset.data.map(() => 0);
            dataset.backgroundColor = generateColors(dataset.data.length);
        }
    });

    // Update chart options
    if (type === 'pie' || type === 'doughnut') {
        chart.options.scales = {};
        chart.options.plugins.legend.display = true;
        chart.options.plugins.legend.position = 'bottom';

        if (type === 'doughnut') {
            chart.data.datasets[0].cutout = '60%';
        }
    } else {
        chart.options.scales = {
            x: {
                display: true,
                title: { display: true, text: 'Bulan' }
            },
            y: {
                display: true,
                title: { display: true, text: 'Jumlah (Rp)' },
                ticks: {
                    callback: function(value) {
                        return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                    }
                }
            }
        };
        chart.options.plugins.legend.display = true;
        chart.options.plugins.legend.position = 'top';
    }

    chart.update('active');
}

function generateColors(count) {
    const colors = [
        '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8',
        '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6c757d'
    ];

    return Array.from({length: count}, (_, i) => colors[i % colors.length]);
}

function toggleChartAnimation(chartId) {
    const chart = Chart.getChart(chartId);
    if (chart) {
        chart.update('active');
        showNotification('Chart animation triggered', 'info', 1500);
    }
}

function updateHeatmapData(dataType) {
    if (!heatmapChart) return;

    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
                   'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];

    let newData, color, label;

    switch (dataType) {
        case 'revenue':
            newData = months.map(() => Math.random() * 100 + 50);
            color = 'rgba(40, 167, 69, ';
            label = 'Revenue Performance';
            break;
        case 'expenses':
            newData = months.map(() => Math.random() * 80 + 30);
            color = 'rgba(220, 53, 69, ';
            label = 'Expense Control';
            break;
        case 'profit':
            newData = months.map(() => Math.random() * 90 + 40);
            color = 'rgba(0, 123, 255, ';
            label = 'Profit Margin';
            break;
    }

    heatmapChart.data.datasets[0].data = newData;
    heatmapChart.data.datasets[0].label = label;
    heatmapChart.data.datasets[0].backgroundColor = function(context) {
        const value = context.parsed.y;
        const alpha = value / 150;
        return color + alpha + ')';
    };

    heatmapChart.update('active');
    showNotification(`Heatmap updated for ${dataType}`, 'success', 2000);
}

function toggleTrendLine() {
    const showTrendLine = document.getElementById('showTrendLine').checked;

    if (showTrendLine) {
        // Add trend line dataset
        const trendData = calculateTrendLine(scatterChart.data.datasets[0].data);
        scatterChart.data.datasets.push({
            label: 'Trend Line',
            data: trendData,
            type: 'line',
            borderColor: '#dc3545',
            borderWidth: 2,
            borderDash: [5, 5],
            fill: false,
            pointRadius: 0
        });
    } else {
        // Remove trend line dataset
        scatterChart.data.datasets = scatterChart.data.datasets.filter(dataset =>
            dataset.label !== 'Trend Line'
        );
    }

    scatterChart.update();
}

function calculateTrendLine(data) {
    // Simple linear regression
    const n = data.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    data.forEach(point => {
        sumX += point.x;
        sumY += point.y;
        sumXY += point.x * point.y;
        sumXX += point.x * point.x;
    });

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    const minX = Math.min(...data.map(p => p.x));
    const maxX = Math.max(...data.map(p => p.x));

    return [
        { x: minX, y: slope * minX + intercept },
        { x: maxX, y: slope * maxX + intercept }
    ];
}

function toggleFullscreen(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    if (!document.fullscreenElement) {
        container.requestFullscreen().then(() => {
            container.style.padding = '20px';
            showNotification('Fullscreen mode enabled', 'info', 2000);
        });
    } else {
        document.exitFullscreen().then(() => {
            container.style.padding = '';
            showNotification('Fullscreen mode disabled', 'info', 2000);
        });
    }
}

function exportChart(chartId) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;

    const url = canvas.toDataURL('image/png');
    const a = document.createElement('a');
    a.href = url;
    a.download = `${chartId}_${new Date().toISOString().split('T')[0]}.png`;
    a.click();

    showNotification('Chart exported successfully', 'success', 2000);
}

function exportChartData() {
    const chart = Chart.getChart('chartLaporan');
    if (!chart) return;

    const data = {
        labels: chart.data.labels,
        datasets: chart.data.datasets.map(dataset => ({
            label: dataset.label,
            data: dataset.data
        }))
    };

    const jsonData = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `chart_data_${new Date().toISOString().split('T')[0]}.json`;
    a.click();

    URL.revokeObjectURL(url);
    showNotification('Chart data exported successfully', 'success', 2000);
}

function shareChart() {
    const canvas = document.getElementById('chartLaporan');
    if (!canvas) return;

    canvas.toBlob(blob => {
        if (navigator.share) {
            const file = new File([blob], 'financial_chart.png', { type: 'image/png' });
            navigator.share({
                title: 'Financial Chart',
                text: 'Check out this financial analysis chart',
                files: [file]
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.write([
                new ClipboardItem({ 'image/png': blob })
            ]).then(() => {
                showNotification('Chart copied to clipboard', 'success', 2000);
            });
        }
    });
}

function resetChartZoom() {
    const chart = Chart.getChart('chartLaporan');
    if (chart && chart.resetZoom) {
        chart.resetZoom();
        showNotification('Chart zoom reset', 'info', 1500);
    }
}

function updateHeatmapView() {
    // Switch main chart to heatmap view
    showNotification('Heatmap view activated', 'info', 2000);
}

// Interactive Charts & Drill-down Functions
let drilldownData = {};
let currentDrillLevel = 'year';
let breadcrumbPath = [];

function initializeInteractiveFeatures() {
    // Add click handlers to charts for drill-down
    addChartClickHandlers();

    // Initialize drill-down data structure
    initializeDrilldownData();

    // Setup cross-filtering
    setupCrossFiltering();
}

function addChartClickHandlers() {
    // Add click handler to main chart
    const mainChart = Chart.getChart('chartLaporan');
    if (mainChart) {
        mainChart.options.onClick = function(event, elements) {
            if (elements.length > 0) {
                const element = elements[0];
                const dataIndex = element.index;
                const label = this.data.labels[dataIndex];

                handleChartDrillDown('main', label, dataIndex);
            }
        };

        // Add hover effects
        mainChart.options.onHover = function(event, elements) {
            event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
        };

        mainChart.update();
    }

    // Add click handler to pie chart
    if (pieChart) {
        pieChart.options.onClick = function(event, elements) {
            if (elements.length > 0) {
                const element = elements[0];
                const dataIndex = element.index;
                const label = this.data.labels[dataIndex];

                handleChartDrillDown('pie', label, dataIndex);
            }
        };

        pieChart.options.onHover = function(event, elements) {
            event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
        };

        pieChart.update();
    }
}

function initializeDrilldownData() {
    // Sample drill-down data structure
    drilldownData = {
        year: {
            'Januari': {
                weeks: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                data: [5000000, 6000000, 5500000, 7000000]
            },
            'Februari': {
                weeks: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                data: [5200000, 6200000, 5700000, 7200000]
            },
            'Maret': {
                weeks: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                data: [5400000, 6400000, 5900000, 7400000]
            }
            // Add more months...
        },
        categories: {
            'Pendapatan Langganan': {
                subcategories: ['Paket Basic', 'Paket Premium', 'Paket Enterprise'],
                data: [40, 35, 25]
            },
            'Pendapatan Non-Langganan': {
                subcategories: ['Installation Fee', 'Maintenance', 'Consultation'],
                data: [50, 30, 20]
            }
        }
    };
}

function handleChartDrillDown(chartType, label, dataIndex) {
    showLoadingState(true);

    // Add to breadcrumb
    breadcrumbPath.push({
        level: currentDrillLevel,
        label: label,
        chartType: chartType
    });

    // Update breadcrumb UI
    updateBreadcrumb();

    // Simulate API call for drill-down data
    setTimeout(() => {
        if (chartType === 'main' && currentDrillLevel === 'year') {
            // Drill down from year to month to weeks
            drillDownToWeeks(label);
        } else if (chartType === 'pie') {
            // Drill down from category to subcategories
            drillDownToSubcategories(label);
        }

        showLoadingState(false);
        showNotification(`Drilled down to ${label}`, 'info', 2000);
    }, 1000);
}

function drillDownToWeeks(monthLabel) {
    const weekData = drilldownData.year[monthLabel];
    if (!weekData) return;

    const mainChart = Chart.getChart('chartLaporan');
    if (!mainChart) return;

    // Update chart with weekly data
    mainChart.data.labels = weekData.weeks;
    mainChart.data.datasets[0].data = weekData.data;
    mainChart.data.datasets[0].label = `${monthLabel} - Weekly Revenue`;

    // Update chart title
    const chartContainer = document.getElementById('mainChartContainer');
    const titleElement = chartContainer.querySelector('.card-title');
    if (titleElement) {
        titleElement.innerHTML = `<i class="bx bx-line-chart me-2"></i>Weekly Revenue - ${monthLabel}`;
    }

    currentDrillLevel = 'week';
    mainChart.update('active');

    // Show drill-up button
    showDrillUpButton();
}

function drillDownToSubcategories(categoryLabel) {
    const categoryData = drilldownData.categories[categoryLabel];
    if (!categoryData) return;

    // Create new chart for subcategories
    createDrillDownModal(categoryLabel, categoryData);
}

function createDrillDownModal(title, data) {
    // Create modal for drill-down view
    const modalHtml = `
        <div class="modal fade" id="drillDownModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bx bx-zoom-in me-2"></i>Drill-down: ${title}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <canvas id="drillDownChart" height="300"></canvas>
                            </div>
                            <div class="col-md-4">
                                <h6>Details</h6>
                                <div id="drillDownDetails"></div>
                                <hr>
                                <button class="btn btn-sm btn-primary" onclick="exportDrillDownData()">
                                    <i class="bx bx-download me-1"></i>Export
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="shareDrillDown()">
                                    <i class="bx bx-share me-1"></i>Share
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="drillDownFurther()">
                            <i class="bx bx-zoom-in me-1"></i>Drill Further
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('drillDownModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('drillDownModal'));
    modal.show();

    // Create chart in modal
    setTimeout(() => {
        createDrillDownChart(data);
        updateDrillDownDetails(data);
    }, 300);
}

function createDrillDownChart(data) {
    const ctx = document.getElementById('drillDownChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.subcategories,
            datasets: [{
                label: 'Distribution',
                data: data.data,
                backgroundColor: [
                    'rgba(0, 123, 255, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ],
                borderWidth: 2,
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed.y}%`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Percentage (%)'
                    }
                }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const element = elements[0];
                    const label = this.data.labels[element.index];
                    showNotification(`Clicked on ${label}`, 'info', 1500);
                }
            }
        }
    });
}

function updateDrillDownDetails(data) {
    const detailsContainer = document.getElementById('drillDownDetails');
    if (!detailsContainer) return;

    let detailsHtml = '';
    data.subcategories.forEach((category, index) => {
        detailsHtml += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="d-flex align-items-center">
                    <span class="badge bg-primary me-2" style="width: 12px; height: 12px;"></span>
                    ${category}
                </span>
                <span class="fw-bold">${data.data[index]}%</span>
            </div>
        `;
    });

    detailsContainer.innerHTML = detailsHtml;
}

function updateBreadcrumb() {
    // Create or update breadcrumb navigation
    let breadcrumbContainer = document.getElementById('chartBreadcrumb');

    if (!breadcrumbContainer) {
        // Create breadcrumb container
        const chartContainer = document.getElementById('mainChartContainer');
        const cardHeader = chartContainer.closest('.card').querySelector('.card-header');

        const breadcrumbHtml = `
            <nav aria-label="Chart navigation" class="mt-2">
                <ol class="breadcrumb mb-0" id="chartBreadcrumb">
                    <li class="breadcrumb-item">
                        <a href="#" onclick="resetToMainView()" class="text-decoration-none">
                            <i class="bx bx-home me-1"></i>Overview
                        </a>
                    </li>
                </ol>
            </nav>
        `;

        cardHeader.insertAdjacentHTML('beforeend', breadcrumbHtml);
        breadcrumbContainer = document.getElementById('chartBreadcrumb');
    }

    // Update breadcrumb items
    let breadcrumbHtml = `
        <li class="breadcrumb-item">
            <a href="#" onclick="resetToMainView()" class="text-decoration-none">
                <i class="bx bx-home me-1"></i>Overview
            </a>
        </li>
    `;

    breadcrumbPath.forEach((item, index) => {
        const isLast = index === breadcrumbPath.length - 1;
        breadcrumbHtml += `
            <li class="breadcrumb-item ${isLast ? 'active' : ''}">
                ${isLast ?
                    `<span>${item.label}</span>` :
                    `<a href="#" onclick="drillUpTo(${index})" class="text-decoration-none">${item.label}</a>`
                }
            </li>
        `;
    });

    breadcrumbContainer.innerHTML = breadcrumbHtml;
}

function showDrillUpButton() {
    const chartContainer = document.getElementById('mainChartContainer');
    const cardHeader = chartContainer.closest('.card').querySelector('.card-header');

    // Check if button already exists
    if (cardHeader.querySelector('#drillUpButton')) return;

    const buttonHtml = `
        <button class="btn btn-sm btn-outline-secondary me-2" id="drillUpButton" onclick="drillUp()">
            <i class="bx bx-arrow-back me-1"></i>Back
        </button>
    `;

    const titleElement = cardHeader.querySelector('.card-title');
    titleElement.insertAdjacentHTML('afterend', buttonHtml);
}

function drillUp() {
    if (breadcrumbPath.length === 0) return;

    // Remove last breadcrumb item
    breadcrumbPath.pop();

    if (breadcrumbPath.length === 0) {
        resetToMainView();
    } else {
        // Go back to previous level
        const previousLevel = breadcrumbPath[breadcrumbPath.length - 1];
        // Implement logic to restore previous view
        showNotification(`Returned to ${previousLevel.label}`, 'info', 1500);
    }

    updateBreadcrumb();
}

function drillUpTo(index) {
    // Remove breadcrumb items after the clicked index
    breadcrumbPath = breadcrumbPath.slice(0, index + 1);

    if (breadcrumbPath.length === 0) {
        resetToMainView();
    } else {
        // Restore view to the clicked level
        const targetLevel = breadcrumbPath[index];
        showNotification(`Returned to ${targetLevel.label}`, 'info', 1500);
    }

    updateBreadcrumb();
}

function resetToMainView() {
    // Reset to original chart view
    breadcrumbPath = [];
    currentDrillLevel = 'year';

    // Remove drill-up button
    const drillUpButton = document.getElementById('drillUpButton');
    if (drillUpButton) {
        drillUpButton.remove();
    }

    // Remove breadcrumb
    const breadcrumbContainer = document.getElementById('chartBreadcrumb');
    if (breadcrumbContainer) {
        breadcrumbContainer.closest('nav').remove();
    }

    // Restore original chart data
    const mainChart = Chart.getChart('chartLaporan');
    if (mainChart) {
        // Restore original data (you would fetch this from your original data source)
        location.reload(); // Simple approach - reload the page
    }

    showNotification('Returned to main view', 'info', 1500);
}

function setupCrossFiltering() {
    // Setup cross-filtering between charts
    // When one chart is filtered, update related charts

    // Example: When pie chart segment is clicked, filter main chart
    // This would be implemented based on your specific requirements
}

function exportDrillDownData() {
    showNotification('Drill-down data export feature coming soon', 'info');
}

function shareDrillDown() {
    showNotification('Drill-down share feature coming soon', 'info');
}

function drillDownFurther() {
    showNotification('Further drill-down feature coming soon', 'info');
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Initialize mini chart canvas
    initializeMiniChart();

    // Initialize advanced charts
    setTimeout(() => {
        initializeAdvancedCharts();
        initializeInteractiveFeatures();
    }, 1000);

    // Start real-time updates
    startRealTimeUpdates();

    // Load saved presets
    loadSavedPresets();

    // Setup auto-refresh toggle
    setupAutoRefreshToggle();

    // Add CSS animations
    addCustomStyles();

    // Initialize mobile features
    initializeMobileFeatures();
    addMobileEventListeners();
});

function initializeMiniChart() {
    const canvas = document.getElementById('miniChart');
    if (canvas) {
        miniChart = canvas.getContext('2d');
        canvas.style.border = '1px solid rgba(255,255,255,0.3)';
        canvas.style.borderRadius = '4px';
    }
}

function setupAutoRefreshToggle() {
    const autoRefreshCheckbox = document.getElementById('autoRefresh');
    if (autoRefreshCheckbox) {
        autoRefreshCheckbox.addEventListener('change', function() {
            if (this.checked) {
                startRealTimeUpdates();
                showLiveNotification('Auto-refresh enabled', 'success', 2000);
            } else {
                stopRealTimeUpdates();
                showLiveNotification('Auto-refresh disabled', 'info', 2000);
            }
        });
    }
}

function addCustomStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes fadeInOut {
            0% { opacity: 0; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
            100% { opacity: 0; transform: scale(0.8); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .real-time-indicator {
            animation: pulse 2s infinite;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .value-change-indicator {
            animation: fadeInOut 2s ease-out;
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .card-hover {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .mini-chart-container {
            position: relative;
            overflow: hidden;
        }

        .connection-status {
            transition: all 0.3s ease;
        }

        .system-status-online {
            animation: pulse 2s infinite;
        }

        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        .notification-exit {
            animation: slideOutRight 0.3s ease-in;
        }

        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .mobile-stack {
                flex-direction: column !important;
            }

            .mobile-full-width {
                width: 100% !important;
                margin-bottom: 10px;
            }

            .mobile-hide {
                display: none !important;
            }

            .mobile-text-center {
                text-align: center !important;
            }

            .mobile-padding {
                padding: 10px !important;
            }

            .card-body {
                padding: 15px !important;
            }

            .btn-group-mobile {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .btn-group-mobile .btn {
                width: 100%;
                margin-bottom: 5px;
            }

            .table-responsive {
                font-size: 12px;
            }

            .chart-container-mobile {
                height: 250px !important;
            }

            .mobile-scroll {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .touch-friendly {
                min-height: 44px;
                min-width: 44px;
            }

            .mobile-fab {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                border-radius: 50%;
                width: 56px;
                height: 56px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            }

            .mobile-bottom-nav {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                border-top: 1px solid #dee2e6;
                padding: 10px;
                z-index: 999;
            }

            .swipe-indicator {
                position: absolute;
                top: 10px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 4px;
                background: #dee2e6;
                border-radius: 2px;
            }
        }

        @media (max-width: 576px) {
            .container-xxl {
                padding-left: 10px !important;
                padding-right: 10px !important;
            }

            .card {
                margin-bottom: 15px;
            }

            .row {
                margin-left: -5px;
                margin-right: -5px;
            }

            .col-12, .col-md-6, .col-xl-3, .col-xl-4, .col-xl-6, .col-xl-8 {
                padding-left: 5px;
                padding-right: 5px;
            }

            h4 {
                font-size: 1.1rem;
            }

            h5 {
                font-size: 1rem;
            }

            h6 {
                font-size: 0.9rem;
            }

            .btn-sm {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
        }
    `;
    document.head.appendChild(style);
}

// Mobile-specific functions
function initializeMobileFeatures() {
    if (isMobileDevice()) {
        setupMobileNavigation();
        setupTouchGestures();
        setupMobileCharts();
        setupMobileFilters();
        addMobileFAB();
        optimizeForMobile();
    }
}

function isMobileDevice() {
    return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

function setupMobileNavigation() {
    // Add mobile bottom navigation
    const bottomNavHtml = `
        <div class="mobile-bottom-nav d-md-none" id="mobileBottomNav">
            <div class="row text-center">
                <div class="col-3">
                    <button class="btn btn-link p-2 touch-friendly" onclick="scrollToSection('kpi')">
                        <i class="bx bx-tachometer d-block"></i>
                        <small>KPI</small>
                    </button>
                </div>
                <div class="col-3">
                    <button class="btn btn-link p-2 touch-friendly" onclick="scrollToSection('charts')">
                        <i class="bx bx-bar-chart-alt-2 d-block"></i>
                        <small>Charts</small>
                    </button>
                </div>
                <div class="col-3">
                    <button class="btn btn-link p-2 touch-friendly" onclick="scrollToSection('predictions')">
                        <i class="bx bx-crystal-ball d-block"></i>
                        <small>Forecast</small>
                    </button>
                </div>
                <div class="col-3">
                    <button class="btn btn-link p-2 touch-friendly" onclick="toggleMobileMenu()">
                        <i class="bx bx-menu d-block"></i>
                        <small>Menu</small>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', bottomNavHtml);

    // Add padding to body to account for bottom nav
    document.body.style.paddingBottom = '80px';
}

function setupTouchGestures() {
    let startX, startY, currentX, currentY;
    let isSwipeGesture = false;

    // Add swipe gestures for chart navigation
    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isSwipeGesture = true;
    }, { passive: true });

    document.addEventListener('touchmove', function(e) {
        if (!isSwipeGesture) return;

        currentX = e.touches[0].clientX;
        currentY = e.touches[0].clientY;

        // Prevent default if horizontal swipe
        const deltaX = Math.abs(currentX - startX);
        const deltaY = Math.abs(currentY - startY);

        if (deltaX > deltaY && deltaX > 30) {
            e.preventDefault();
        }
    }, { passive: false });

    document.addEventListener('touchend', function(e) {
        if (!isSwipeGesture) return;

        const deltaX = currentX - startX;
        const deltaY = currentY - startY;

        // Check if it's a horizontal swipe
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
            if (deltaX > 0) {
                // Swipe right - previous chart
                navigateChart('previous');
            } else {
                // Swipe left - next chart
                navigateChart('next');
            }
        }

        isSwipeGesture = false;
    }, { passive: true });
}

function setupMobileCharts() {
    // Optimize charts for mobile
    const charts = ['chartLaporan', 'pieChart', 'donutChart', 'scatterChart', 'heatmapChart'];

    charts.forEach(chartId => {
        const chart = Chart.getChart(chartId);
        if (chart) {
            // Update chart options for mobile
            chart.options.responsive = true;
            chart.options.maintainAspectRatio = false;

            // Reduce font sizes
            if (chart.options.plugins && chart.options.plugins.legend) {
                chart.options.plugins.legend.labels.font = { size: 10 };
            }

            // Simplify tooltips
            if (chart.options.plugins && chart.options.plugins.tooltip) {
                chart.options.plugins.tooltip.titleFont = { size: 11 };
                chart.options.plugins.tooltip.bodyFont = { size: 10 };
            }

            // Reduce tick font sizes
            if (chart.options.scales) {
                Object.keys(chart.options.scales).forEach(scaleKey => {
                    if (chart.options.scales[scaleKey].ticks) {
                        chart.options.scales[scaleKey].ticks.font = { size: 9 };
                    }
                });
            }

            chart.update();
        }
    });
}

function setupMobileFilters() {
    // Convert filter dropdowns to mobile-friendly format
    const filterForm = document.getElementById('filterForm');
    if (filterForm && isMobileDevice()) {
        // Add mobile filter toggle
        const mobileFilterToggle = `
            <div class="d-md-none mb-3">
                <button class="btn btn-outline-primary w-100 touch-friendly" onclick="toggleMobileFilters()">
                    <i class="bx bx-filter me-2"></i>Filters & Options
                    <i class="bx bx-chevron-down ms-auto"></i>
                </button>
            </div>
        `;

        filterForm.insertAdjacentHTML('beforebegin', mobileFilterToggle);

        // Hide filters by default on mobile
        filterForm.style.display = 'none';
        filterForm.classList.add('mobile-filter-panel');
    }
}

function addMobileFAB() {
    // Add floating action button for quick actions
    const fabHtml = `
        <div class="mobile-fab d-md-none">
            <button class="btn btn-primary rounded-circle touch-friendly" onclick="toggleQuickActions()">
                <i class="bx bx-plus fs-4"></i>
            </button>
        </div>

        <div class="mobile-quick-actions" id="mobileQuickActions" style="display: none;">
            <div class="position-fixed" style="bottom: 90px; right: 20px; z-index: 1001;">
                <div class="d-flex flex-column gap-2">
                    <button class="btn btn-success rounded-circle touch-friendly" onclick="exportToExcel()" title="Export">
                        <i class="bx bx-download"></i>
                    </button>
                    <button class="btn btn-info rounded-circle touch-friendly" onclick="generatePredictions()" title="Forecast">
                        <i class="bx bx-crystal-ball"></i>
                    </button>
                    <button class="btn btn-warning rounded-circle touch-friendly" onclick="window.print()" title="Print">
                        <i class="bx bx-printer"></i>
                    </button>
                    <button class="btn btn-secondary rounded-circle touch-friendly" onclick="toggleMobileFilters()" title="Filters">
                        <i class="bx bx-filter"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', fabHtml);
}

function optimizeForMobile() {
    // Add mobile-specific classes
    document.querySelectorAll('.card-header .d-flex').forEach(header => {
        header.classList.add('mobile-stack');
    });

    document.querySelectorAll('.btn-group').forEach(btnGroup => {
        btnGroup.classList.add('btn-group-mobile');
    });

    // Make tables more mobile-friendly
    document.querySelectorAll('.table-responsive').forEach(table => {
        table.classList.add('mobile-scroll');
    });

    // Add swipe indicators to charts
    document.querySelectorAll('.card-body canvas').forEach(canvas => {
        const container = canvas.closest('.card-body');
        if (container && !container.querySelector('.swipe-indicator')) {
            container.style.position = 'relative';
            container.insertAdjacentHTML('afterbegin', '<div class="swipe-indicator d-md-none"></div>');
        }
    });
}

function scrollToSection(sectionId) {
    let targetElement;

    switch (sectionId) {
        case 'kpi':
            targetElement = document.querySelector('[data-section="kpi"]') ||
                           document.querySelector('.card-title:contains("Key Performance")').closest('.card');
            break;
        case 'charts':
            targetElement = document.querySelector('[data-section="charts"]') ||
                           document.getElementById('mainChartContainer').closest('.card');
            break;
        case 'predictions':
            targetElement = document.querySelector('[data-section="predictions"]') ||
                           document.querySelector('.card-title:contains("Prediktif")').closest('.card');
            break;
    }

    if (targetElement) {
        targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Highlight the section briefly
        targetElement.style.transition = 'box-shadow 0.3s ease';
        targetElement.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.3)';
        setTimeout(() => {
            targetElement.style.boxShadow = '';
        }, 1500);
    }
}

function toggleMobileMenu() {
    // Show mobile menu modal
    const menuModalHtml = `
        <div class="modal fade" id="mobileMenuModal" tabindex="-1">
            <div class="modal-dialog modal-fullscreen-sm-down">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Menu</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action touch-friendly" onclick="exportToExcel()">
                                <i class="bx bx-download me-3"></i>Export Data
                            </a>
                            <a href="#" class="list-group-item list-group-item-action touch-friendly" onclick="generatePredictions()">
                                <i class="bx bx-crystal-ball me-3"></i>Generate Forecast
                            </a>
                            <a href="#" class="list-group-item list-group-item-action touch-friendly" onclick="window.print()">
                                <i class="bx bx-printer me-3"></i>Print Report
                            </a>
                            <a href="#" class="list-group-item list-group-item-action touch-friendly" onclick="toggleMobileFilters()">
                                <i class="bx bx-filter me-3"></i>Filters & Options
                            </a>
                            <a href="#" class="list-group-item list-group-item-action touch-friendly" onclick="resetToMainView()">
                                <i class="bx bx-refresh me-3"></i>Reset View
                            </a>
                            <a href="#" class="list-group-item list-group-item-action touch-friendly" onclick="toggleFullscreen('mainChartContainer')">
                                <i class="bx bx-fullscreen me-3"></i>Fullscreen
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('mobileMenuModal');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.insertAdjacentHTML('beforeend', menuModalHtml);
    const modal = new bootstrap.Modal(document.getElementById('mobileMenuModal'));
    modal.show();
}

function toggleMobileFilters() {
    const filterForm = document.getElementById('filterForm');
    const isVisible = filterForm.style.display !== 'none';

    if (isVisible) {
        filterForm.style.display = 'none';
        showNotification('Filters hidden', 'info', 1500);
    } else {
        filterForm.style.display = 'block';
        filterForm.scrollIntoView({ behavior: 'smooth' });
        showNotification('Filters shown', 'info', 1500);
    }
}

function toggleQuickActions() {
    const quickActions = document.getElementById('mobileQuickActions');
    const fab = document.querySelector('.mobile-fab button i');

    if (quickActions.style.display === 'none') {
        quickActions.style.display = 'block';
        fab.className = 'bx bx-x fs-4';
        fab.style.transform = 'rotate(180deg)';
    } else {
        quickActions.style.display = 'none';
        fab.className = 'bx bx-plus fs-4';
        fab.style.transform = 'rotate(0deg)';
    }
}

function navigateChart(direction) {
    const chartTypes = ['line', 'bar', 'area', 'pie', 'donut'];
    const currentIndex = chartTypes.indexOf(currentChartType);

    let newIndex;
    if (direction === 'next') {
        newIndex = (currentIndex + 1) % chartTypes.length;
    } else {
        newIndex = (currentIndex - 1 + chartTypes.length) % chartTypes.length;
    }

    const chartSelector = document.getElementById('chartTypeSelector');
    if (chartSelector) {
        chartSelector.value = chartTypes[newIndex];
        changeChartType();
    }

    showNotification(`Switched to ${chartTypes[newIndex]} chart`, 'info', 1500);
}

// Add mobile-specific event listeners
function addMobileEventListeners() {
    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Resize charts after orientation change
            Chart.helpers.each(Chart.instances, function(instance) {
                instance.resize();
            });
        }, 500);
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (isMobileDevice()) {
            optimizeForMobile();
        }
    });

    // Add pull-to-refresh (simplified)
    let startY = 0;
    let pullDistance = 0;

    document.addEventListener('touchstart', function(e) {
        if (window.scrollY === 0) {
            startY = e.touches[0].clientY;
        }
    });

    document.addEventListener('touchmove', function(e) {
        if (window.scrollY === 0 && startY > 0) {
            pullDistance = e.touches[0].clientY - startY;

            if (pullDistance > 100) {
                // Show pull-to-refresh indicator
                showNotification('Release to refresh', 'info', 1000);
            }
        }
    });

    document.addEventListener('touchend', function(e) {
        if (pullDistance > 100) {
            // Trigger refresh
            location.reload();
        }
        startY = 0;
        pullDistance = 0;
    });
}

function loadSavedPresets() {
    const savedPresets = JSON.parse(localStorage.getItem('filterPresets') || '{}');
    if (Object.keys(savedPresets).length > 0) {
        // Add saved presets to quick presets section
        const presetsContainer = document.querySelector('.d-flex.flex-wrap.gap-2');
        if (presetsContainer) {
            Object.keys(savedPresets).forEach(presetName => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-sm btn-outline-dark';
                button.innerHTML = `<i class="bx bx-bookmark me-1"></i>${presetName}`;
                button.onclick = () => loadSavedPreset(presetName);
                presetsContainer.appendChild(button);
            });
        }
    }
}

function loadSavedPreset(presetName) {
    const savedPresets = JSON.parse(localStorage.getItem('filterPresets') || '{}');
    const preset = savedPresets[presetName];

    if (preset) {
        Object.keys(preset).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = preset[key];
            }
        });

        showNotification(`Preset "${presetName}" dimuat`, 'success');
    }
}
</script>

<!-- Include XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

@endsection