<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Paket;
use App\Models\Status;
use App\Models\Pembayaran;
use App\Models\Metode;
use App\Models\Pendapatan;
use Carbon\Carbon;
use App\Services\MikrotikServices;
use Paginate;
use Illuminate\Support\Facades\Storage;
use App\Models\Kas;
use App\Models\Perusahaan;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Services\ChatServices;
use App\Models\Pengeluaran;
use App\Models\Rab;


class KeuanganController extends Controller
{

    public function dashboardKeuangan()
    {
        // Calculate financial metrics
        $subs = Pembayaran::sum('jumlah_bayar');
        $corp = Perusahaan::where('status_id', 3)->sum('harga');
        $nonSubs = Pendapatan::sum('jumlah_pendapatan');
        // dd($corp + $subs + $nonSubs);
        $totalFull = $subs + $corp + $nonSubs;
        $totalSubs = $subs + $corp;

        $totalRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->sum('tagihan');

        $monthlyRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->sum('tagihan');

        $pendingRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->sum('tagihan');

        $totalTransactions = Pembayaran::count();

        // Monthly statistics
        $monthlyPaid = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->count();

        $monthlyUnpaid = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->count();

        // Calculate percentages
        $totalInvoices = Invoice::count();
        $paidCount = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->count();

        $pendingCount = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->count();

        $paidPercentage = $totalInvoices > 0 ? round(($paidCount / $totalInvoices) * 100, 1) : 0;
        $pendingPercentage = $totalInvoices > 0 ? round(($pendingCount / $totalInvoices) * 100, 1) : 0;
        $overduePercentage = max(0, 100 - $paidPercentage - $pendingPercentage);

        // Get revenue trends data for the last 6 months
        $monthlyData = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $month = $date->format('M');
            $year = $date->format('Y');
            
            // Get subscription revenue
            $subscriptionRevenue = Pembayaran::whereMonth('tanggal_bayar', $date->month)
                ->whereYear('tanggal_bayar', $date->year)
                ->sum('jumlah_bayar');

            // Get non-subscription revenue
            $nonSubscriptionRevenue = Pendapatan::whereMonth('tanggal', $date->month)
                ->whereYear('tanggal', $date->year)
                ->sum('jumlah_pendapatan');

            $monthlyData['labels'][] = $month . ' ' . $year;
            $monthlyData['subscription'][] = $subscriptionRevenue;
            $monthlyData['nonSubscription'][] = $nonSubscriptionRevenue;
        }

        return view('/keuangan/dashboard-keuangan/dashboard-keuangan', [
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'totalRevenue' => $totalRevenue,
            'monthlyRevenue' => $monthlyRevenue,
            'pendingRevenue' => $pendingRevenue,
            'totalTransactions' => $totalTransactions,
            'monthlyPaid' => $monthlyPaid,
            'monthlyUnpaid' => $monthlyUnpaid,
            'paidPercentage' => $paidPercentage,
            'pendingPercentage' => $pendingPercentage,
            'overduePercentage' => $overduePercentage,
            'totalFull' => $totalFull,
            'subs' => $totalSubs,
            'nonSubs' => $nonSubs,
            'monthlyData' => $monthlyData
        ]);
    }


    public function index(Request $request)
    {
        // Get filter parameters
        $search = $request->get('search');
        $status = $request->get('status');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $bulan = $request->get('bulan');
        // dd($bulan);

        // Build query for invoices with relationships
        $query = Invoice::with(['customer', 'paket', 'status'])
            ->orderBy('created_at', 'desc')->whereIn('status_id', [1, 7]); // Exclude 'Dibatalkan' status

        // Apply search filter
        if ($search) {
            $query->whereHas('customer', function($q) use ($search) {
                $q->where('nama_customer', 'like', '%' . $search . '%');
            })->orWhereHas('paket', function($q) use ($search) {
                $q->where('nama_paket', 'like', '%' . $search . '%');
            });
        }

        // Apply status filter
        if ($status) {
            $query->where('status_id', $status);
        }

        // Apply month filter
        if ($bulan) {
            $query->whereMonth('jatuh_tempo', $bulan);
        }

        // Apply date range filter
        if ($startDate && $endDate) {
            $query->whereBetween('jatuh_tempo', [
                Carbon::parse($startDate)->startOfDay(),
                Carbon::parse($endDate)->endOfDay()
            ]);
        }
        

        $invoices = $query->paginate(10);

        // Calculate revenue statistics
        $totalRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->sum('tagihan') + Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->sum('tambahan');

        $monthlyRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->sum('tagihan') + Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->sum('tambahan');

        $pendingRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->sum('tagihan') + Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->sum('tambahan');

        $totalInvoices = Invoice::where('status_id', 7)->count();

        // Get all status options for filter dropdown
        $statusOptions = Status::whereIn('id', [7, 8])->get();

        $metode = Metode::whereNot('id', 3)->get();
        $pendapatan = Pendapatan::paginate(5);
        $agen = User::where('roles_id', 6)->count();
        $totalPembayaran = Pembayaran::where('status_id', 1)->count();
        // dd($agen);

        return view('/keuangan/data-pendapatan',[
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'invoices' => $invoices,
            'totalRevenue' => $totalRevenue,
            'monthlyRevenue' => $monthlyRevenue,
            'pendingRevenue' => $pendingRevenue,
            'totalInvoices' => $totalInvoices,
            'statusOptions' => $statusOptions,
            'search' => $search,
            'status' => $status,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'metode' => $metode,
            'pendapatan' => $pendapatan,
            'agen' => $agen,
            'totalPembayaran' => $totalPembayaran
        ]);
    }

    public function getRevenueData(Request $request)
    {
        // For AJAX requests to get updated data
        $search = $request->get('search');
        $status = $request->get('status');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        $query = Invoice::with(['customer', 'paket', 'status'])
            ->orderBy('created_at', 'desc');

        if ($search) {
            $query->whereHas('customer', function($q) use ($search) {
            $q->where('nama_customer', 'like', '%' . $search . '%');
            })->orWhereHas('paket', function($q) use ($search) {
            $q->where('nama_paket', 'like', '%' . $search . '%');
            });
        }

        if ($status) {
            $query->where('status_id', $status);
        }

        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        $invoices = $query->paginate(10);

        if ($request->ajax()) {
            return response()->json([
            'invoices' => $invoices,
            'html' => view('keuangan.partials.revenue-table', compact('invoices'))->render(),
            'pagination' => $invoices->links()->toHtml()
            ]);
        }
    }

    public function pembayaran(Request $request)
    {
        // Get filter parameters
        $search = $request->get('search');
        $metode = $request->get('metode');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // Build query for payments with relationships
        $query = Pembayaran::with(['invoice.customer', 'invoice.paket', 'status', 'user'])
            ->orderBy('created_at', 'desc');

        // Apply search filter
        if ($search) {
            $query->whereHas('invoice.customer', function($q) use ($search) {
                $q->where('nama_customer', 'like', '%' . $search . '%');
            })->orWhereHas('invoice.paket', function($q) use ($search) {
                $q->where('nama_paket', 'like', '%' . $search . '%');
            })->orWhere('metode_bayar', 'like', '%' . $search . '%');
        }

        // Apply payment method filter
        if ($metode) {
            $query->where('metode_bayar', $metode);
        }

        // Apply date range filter
        if ($startDate) {
            $query->whereDate('tanggal_bayar', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('tanggal_bayar', '<=', $endDate);
        }

        $payments = $query->paginate(10);

        // Calculate payment statistics

        $invoicePay = $query->paginate(10);

        $totalPayments = Pembayaran::sum('jumlah_bayar');

        $todayPayments = Pembayaran::whereDate('tanggal_bayar', Carbon::today())
            ->sum('jumlah_bayar');

        $monthlyPayments = Pembayaran::whereMonth('tanggal_bayar', Carbon::now()->month)
            ->whereYear('tanggal_bayar', Carbon::now()->year)
            ->sum('jumlah_bayar');

        $totalTransactions = Pembayaran::count();

        // Get payment methods for filter dropdown
        $paymentMethods = Pembayaran::select('metode_bayar')
            ->distinct()
            ->whereNotNull('metode_bayar')
            ->pluck('metode_bayar');

        // Calculate payment method statistics
        $cashPayments = Pembayaran::where(function($q) {
            $q->where('metode_bayar', 'like', '%cash%')
              ->orWhere('metode_bayar', 'like', '%tunai%');
        })->count(); // Kosongkan dulu sesuai permintaan
        $transferPayments = Pembayaran::where(function($q) {
            $q->where('metode_bayar', 'like', '%transfer%')
              ->orWhere('metode_bayar', 'like', '%bank%')
              ->orWhere('metode_bayar', 'like', '%briva%')
              ->orWhere('metode_bayar', 'like', '%bniva%')
              ->orWhere('metode_bayar', 'like', '%transfer bank%');
        })->count();

        $tripay = Pembayaran::where(function($q) {
            $q->where('metode_bayar', 'like', '%tripay%')
              ->orWhere('metode_bayar', 'like', '%DANA%');
        })->count();

        // E-wallet payments count
        $ewalletPayments = Pembayaran::where(function($q) {
            $q->where('metode_bayar', 'like', '%ewallet%')
              ->orWhere('metode_bayar', 'like', '%e-wallet%')
              ->orWhere('metode_bayar', 'like', '%gopay%')
              ->orWhere('metode_bayar', 'like', '%ovo%')
              ->orWhere('metode_bayar', 'like', '%dana%')
              ->orWhere('metode_bayar', 'like', '%qris%');
        })->count();

        return view('/keuangan/data-pembayaran',[
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'payments' => $payments,
            'totalPayments' => $totalPayments,
            'todayPayments' => $todayPayments,
            'monthlyPayments' => $monthlyPayments,
            'totalTransactions' => $totalTransactions,
            'paymentMethods' => $paymentMethods,
            'cashPayments' => $cashPayments,
            'transferPayments' => $transferPayments,
            'ewalletPayments' => $ewalletPayments,
            'search' => $search,
            'metode' => $metode,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'tripay' => $tripay,
            'invoicePay' => $invoicePay,
        ]);
    }

    public function getDashboardData()
    {
        // Calculate financial metrics
        $totalRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->sum('tagihan');

        $monthlyRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->sum('tagihan');

        $pendingRevenue = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->sum('tagihan');

        $totalTransactions = Pembayaran::count();

        // Monthly statistics
        $monthlyPaid = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->count();

        $monthlyUnpaid = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->count();

        // Calculate percentages
        $totalInvoices = Invoice::count();
        $paidCount = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->count();

        $pendingCount = Invoice::whereHas('status', function($q) {
            $q->where('nama_status', 'Belum Bayar');
        })->count();

        $paidPercentage = $totalInvoices > 0 ? round(($paidCount / $totalInvoices) * 100, 1) : 0;
        $pendingPercentage = $totalInvoices > 0 ? round(($pendingCount / $totalInvoices) * 100, 1) : 0;
        $overduePercentage = max(0, 100 - $paidPercentage - $pendingPercentage);

        // Get revenue data for the last 6 months
        $revenueData = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $revenue = Invoice::whereHas('status', function($q) {
                $q->where('nama_status', 'Sudah Bayar');
            })->whereMonth('created_at', $date->month)
              ->whereYear('created_at', $date->year)
              ->sum('tagihan');
            $revenueData[] = (int) $revenue;
        }

        // Get payment method distribution
        $paymentMethods = Pembayaran::select('metode_bayar')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('metode_bayar')
            ->get();

        $methodData = [0, 0, 0, 0]; // Default for [Transfer Bank, E-Wallet, Cash, Lainnya]
        foreach ($paymentMethods as $method) {
            switch (strtolower($method->metode_bayar)) {
                case 'transfer':
                case 'bank':
                case 'transfer bank':
                    $methodData[0] += $method->count;
                    break;
                case 'ewallet':
                case 'e-wallet':
                case 'gopay':
                case 'ovo':
                case 'dana':
                    $methodData[1] += $method->count;
                    break;
                case 'cash':
                case 'tunai':
                    $methodData[2] += $method->count;
                    break;
                default:
                    $methodData[3] += $method->count;
                    break;
            }
        }

        return response()->json([
            'totalRevenue' => (int) $totalRevenue,
            'monthlyRevenue' => (int) $monthlyRevenue,
            'pendingPayments' => (int) $pendingRevenue,
            'totalTransactions' => $totalTransactions,
            'monthlyPaid' => $monthlyPaid,
            'monthlyUnpaid' => $monthlyUnpaid,
            'paidPercentage' => $paidPercentage,
            'pendingPercentage' => $pendingPercentage,
            'overduePercentage' => $overduePercentage,
            'revenueData' => $revenueData,
            'paymentMethods' => $methodData,
        ]);
    }

    public function approvePayment(Request $request, $customerId)
    {
        // dd($request->all());
        

        try {
            // Validate the request
            $request->validate([
                'paymentDate' => 'required|date',
                'paymentMethodSelect' => 'required|string',
                'paymentNotes' => 'nullable|string|max:500',
                'transferProof' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
                'invoice_id' => 'nullable|integer'
            ]);

            // Find the customer
            $customer = Customer::findOrFail($customerId);
            // Get the invoice - either from request or find the latest unpaid invoice
            $invoiceId = $request->input('invoice_id');
            if ($invoiceId) {
                $invoice = Invoice::findOrFail($invoiceId);
            } else {
                // Find the latest unpaid invoice for this customer
                $invoice = Invoice::where('customer_id', $customerId)
                    ->whereHas('status', function($q) {
                        $q->where('nama_status', 'Belum Bayar');
                    })
                    ->latest()
                    ->first();

                if (!$invoice) {
                    return redirect()->back()->with('error', 'Tidak ada tagihan yang perlu dibayar untuk pelanggan ini.');
                }
            }

            // Handle file upload for transfer proof
            $buktiPath = null;
            if ($request->hasFile('transferProof')) {
                $file = $request->file('transferProof');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $buktiPath = $file->storeAs('bukti_pembayaran', $fileName, 'public');
            }

            // Get payment method name
            $metode = Metode::find($request->paymentMethodSelect);
            // dd($metode);
            $metodeNama = $metode ? $metode->nama_metode : 'Cash';
            $mikrotik = new MikrotikServices();
            $mikrotik->removeActiveConnections($customer->usersecret);
            $profile = $customer->paket ? $customer->paket->nama_paket: 'profile-test-aplikasi';

            $mikrotik->changeUserProfile($customer->usersecret, $profile);
            $jumlahBayar = $invoice->tagihan + $invoice->tambahan;
            // Create payment record
            $pembayaran = new Pembayaran();
            $pembayaran->invoice_id = $invoice->id;
            $pembayaran->jumlah_bayar = $jumlahBayar;
            $pembayaran->tanggal_bayar = $request->paymentDate;
            $pembayaran->metode_bayar = $metodeNama;
            $pembayaran->keterangan = $request->paymentNotes;
            $pembayaran->bukti_bayar = $buktiPath;
            $pembayaran->status_id = 6; // Sudah Bayar
            $pembayaran->user_id = auth()->id();
            $pembayaran->save();

            // Update invoice status to paid
            $invoice->status_id = 6; // Sudah Bayar
            $invoice->save();

            // Update customer status to active if needed
            if ($customer->status_id != 3) {
                $customer->status_id = 3; // Active
                $customer->save();
            }

            return redirect()->back()->with('success', 'Pembayaran berhasil dikonfirmasi untuk pelanggan ' . $customer->nama_customer);

        } catch (\Exception $e) {
            \Log::error('Error in approvePayment: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan saat memproses pembayaran: ' . $e->getMessage());
        }
    }

    public function dailyPembayaran()
    {
        // Get today's payments
        $today = Carbon::today();
        $payments = Pembayaran::with(['invoice.customer', 'invoice.paket', 'status'])
            ->whereDate('tanggal_bayar', $today)
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        $daily = Pembayaran::selectRaw('DATE(tanggal_bayar) as date, 
            SUM(jumlah_bayar) as total,
            SUM(CASE WHEN metode_bayar LIKE "%cash%" OR metode_bayar LIKE "%tunai%" THEN jumlah_bayar ELSE 0 END) as cash_total,
            SUM(CASE WHEN metode_bayar LIKE "%transfer%" OR metode_bayar LIKE "%bniva%" OR metode_bayar LIKE "%briva%" THEN jumlah_bayar ELSE 0 END) as transfer_total,
            SUM(CASE WHEN metode_bayar LIKE "%tripay%" OR metode_bayar LIKE "%DANA%" OR metode_bayar LIKE "%ewallet%" OR metode_bayar LIKE "%e-wallet%" OR metode_bayar LIKE "%gopay%" OR metode_bayar LIKE "%ovo%" OR metode_bayar LIKE "%qris%" THEN jumlah_bayar ELSE 0 END) as ewallet_total')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();
        // dd($daily);
        // Calculate total payment for today
        $totalToday = $payments->sum('jumlah_bayar');

        return view('/keuangan/pembayaran-daily', [
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'payments' => $payments,
            'totalToday' => $totalToday,
            'daily' => $daily,
        ]);
    }

    public function tambahPendapatan(Request $request)
    {
        // dd($jumlahTotal);
        $jumlahKas = Kas::latest()->value('jumlah_kas');
        // dd($jumlahKas);
        try {
            // Handle file upload for receipt
            $buktiPath = null;
            if ($request->hasFile('bukti_pembayaran')) {
                $file = $request->file('bukti_pembayaran');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $buktiPath = $file->storeAs('bukti_pendapatan', $fileName, 'public');
            }


            // Create revenue record
            $pendapatan = new Pendapatan();
            $pendapatan->jumlah_pendapatan = $request->jumlah_pendapatan_raw;
            $pendapatan->jenis_pendapatan = $request->jenis_pendapatan;
            $pendapatan->deskripsi = $request->deskripsi;
            $pendapatan->tanggal = $request->tanggal;
            $pendapatan->bukti_pendapatan = $buktiPath;
            $pendapatan->metode_bayar = $request->metode_bayar;
            $pendapatan->user_id = auth()->id();
            $pendapatan->save();

            // Update Kas record
            $kas = new Kas();
            $kas->jumlah_kas = $pendapatan->jumlah_pendapatan + $jumlahKas;
            $kas->debit = $pendapatan->jumlah_pendapatan;
            $kas->kas_id = 1;
            $kas->keterangan = 'Pendapatan: ' . $request->jenis_pendapatan . ' - ' . $request->deskripsi;
            $kas->tanggal_kas = $request->tanggal;
            $kas->user_id = auth()->user()->id;
            $kas->status_id = 3;
            $kas->save();

            return redirect()->back()->with('success', 'Pendapatan berhasil ditambahkan');
        } catch (\Exception $e) {
            \Log::error('Error in tambahPendapatan: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan saat menambahkan pendapatan: ' . $e->getMessage());
        }
    }

    public function nonLangganan(Request $request)
    {
        // Get filter parameters
        $search = $request->get('search');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // Build query for payments with relationships
        $query = Pendapatan::with(['user'])
            ->orderBy('created_at', 'desc');

        // Apply search filter
        if ($search) {
            $query->where('jenis_pendapatan', 'like', '%' . $search . '%')
                ->orWhere('deskripsi', 'like', '%' . $search . '%');
        }

        // Apply date range filter
        if ($startDate) {
            $query->whereDate('tanggal', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('tanggal', '<=', $endDate);
        }

        $pendapatan = $query->paginate(10);

        $jumlah = Pendapatan::sum('jumlah_pendapatan');
        $todayRevenue = Pendapatan::whereDate('tanggal', Carbon::today())->sum('jumlah_pendapatan');
        $monthly = Pendapatan::whereMonth('tanggal', Carbon::now()->month)
            ->whereYear('tanggal', Carbon::now()->year)
            ->sum('jumlah_pendapatan');

        // Get all payment methods for filter dropdown
        $cashCount = Pendapatan::where('metode_bayar', 'like', '%cash%')
            ->orWhere('metode_bayar', 'like', '%tunai%')
            ->count();
        $transferCount = Pendapatan::where('metode_bayar', 'like', '%transfer%')
            ->orWhere('metode_bayar', 'like', '%bank%')
            ->orWhere('metode_bayar', 'like', '%transfer bank%')
            ->count();
        $ewalletCount = Pendapatan::where('metode_bayar', 'like', '%ewallet%')
            ->orWhere('metode_bayar', 'like', '%e-wallet%')
            ->orWhere('metode_bayar', 'like', '%gopay%')
            ->orWhere('metode_bayar', 'like', '%ovo%')
            ->orWhere('metode_bayar', 'like', '%dana%')
            ->orWhere('metode_bayar', 'like', '%qris%')
            ->count();

        $metode = Metode::all();

        return view('/keuangan/non-langganan',[
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'pendapatan' => $pendapatan,
            'search' => $search,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'metode' => $metode,
            'jumlah' => $jumlah,
            'jumlahDaily' => $todayRevenue,
            'jumlahMonthly' => $monthly,
            'cashCount' => $cashCount,
            'transferCount' => $transferCount,
            'ewalletCount' => $ewalletCount,
        ]);
    }

    public function searchNonLangganan(Request $request)
    {
        try {
            $search = $request->get('search');
            $startDate = $request->get('start_date');

            $query = Pendapatan::with(['user'])
                ->orderBy('created_at', 'desc');

            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('jenis_pendapatan', 'like', '%' . $search . '%')
                      ->orWhere('deskripsi', 'like', '%' . $search . '%')
                      ->orWhereHas('user', function($q) use ($search) {
                          $q->where('name', 'like', '%' . $search . '%');
                      });
                });
            }

            if ($startDate) {
                $query->whereDate('tanggal', '=', $startDate);
            }

            $pendapatan = $query->paginate(10);

            $html = view('keuangan.partials.pendapatan-table', compact('pendapatan'))->render();

            return response()->json([
                'html' => $html,
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data: ' . $e->getMessage()
            ], 500);
        }
    }
    public function globalPendapatan()
    {
        // $co = Customer::all();
        // dd($co);

        // Ambil semua data pembayaran yang status invoice-nya "Sudah Bayar"
        $pembayaran = Pembayaran::with(['invoice.customer', 'invoice.status', 'invoice.paket'])
            ->whereHas('invoice.status', function ($q) {
                $q->where('nama_status', 'Sudah Bayar');
            })
        ->get();

        $allPembayaran = Pembayaran::with([
            'invoice.customer',
            'invoice.status',
            'invoice.paket'
        ])->whereHas('invoice.status', function ($q) {
            $q->where('nama_status', 'Sudah Bayar');
        })->get();

        // dd($pembayaran);
        // Susun data per customer dan per bulan
        $data = [];

        foreach ($pembayaran as $p) {
            $customer = $p->invoice->customer->nama_customer;
            $alamat = $p->invoice->customer->alamat;
            $paket = $p->invoice->paket ? $p->invoice->paket->nama_paket : 'Tidak Diketahui';
            // dd($total);
            $month = Carbon::parse($p->tanggal_bayar)->format('n'); // 1 - 12

            if (!isset($data[$customer])) {
                $data[$customer] = array_fill(1, 12, 0); // Inisialisasi 12 bulan
                $data[$customer]['total'] = 0; // Inisialisasi total
            }
            $data[$customer]['alamat'] = $alamat;
            $data[$customer]['paket'] = $paket;
            $data[$customer]['total'] += $p->jumlah_bayar; 
            $data[$customer][$month] += $p->jumlah_bayar;
        }
        // Ubah jadi array untuk dikirim ke view
        // dd($data);
        $formatted = [];
        foreach ($data as $customer => $months) {
            $formatted[] = [
            'nama' => $customer,
            'alamat' => $months['alamat'] ?? 'Tidak Diketahui', 
            'bulan' => array_filter($months, fn($v, $k) => is_numeric($k), ARRAY_FILTER_USE_BOTH),
            'paket' => $months['paket'] ?? 'Tidak Diketahui',
            'total' => $months['total'] ?? 0
            ];
        }

        // Convert array to collection and paginate
        $perPage = 10; // Number of items per page
        $currentPage = request()->get('page', 1);
        $collection = collect($formatted);
        
        $paginatedFormatted = new \Illuminate\Pagination\LengthAwarePaginator(
            $collection->forPage($currentPage, $perPage),
            $collection->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url()]
        );

        // Replace original $formatted with paginated version
        $formatted = $paginatedFormatted;

        $bulanTotals = array_fill(1, 12, 0); // Inisialisasi
        
        foreach ($allPembayaran as $p) {
            $month = \Carbon\Carbon::parse($p->tanggal_bayar)->format('n');
            $bulanTotals[$month] += $p->jumlah_bayar;
        }

        $totalPendapatan = array_sum($bulanTotals);
        // dd($totalPendapatan);

        // dd($bulanTotals);
        // Tambahkan total per bulan ke array
        $nonLangganan = Pendapatan::with(['user'])
            ->orderBy('tanggal', 'desc')
            ->get();

        $nonFormatted = [];

        foreach ($nonLangganan as $pendapatan) {
            $user = $pendapatan->jenis_pendapatan ? $pendapatan->jenis_pendapatan : 'Tidak Diketahui';
            $admin = $pendapatan->user_id;
            $a = $pendapatan->user->roles->name ?? 'Tidak Diketahui';
            $month = Carbon::parse($pendapatan->tanggal)->format('n'); // 1 - 12

            if (!isset($nonFormatted[$user])) {
                $nonFormatted[$user] = array_fill(1, 12, 0); // Inisialisasi 12 bulan
            }
            $nonFormatted[$user][$month] += $pendapatan->jumlah_pendapatan;
        }
        // Ubah jadi array untuk dikirim ke view
        $nonFormattedData = [];
        foreach ($nonFormatted as $user => $months) {
            $nonFormattedData[] = [
                'nama' => $user,
                'a' => $a,
                'bulan' => $months // bulan 1-12 => jumlah pendapatan
            ];
        }
        // dd($nonFormattedData);
        $pakets = Paket::all();

        return view('/keuangan/pendapatan-global',[
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'pembayaran' => $pembayaran,
            'formatted' => $formatted,
            'nonFormattedData' => $nonFormattedData,
            'pakets' => $pakets,
            'bulanTotals' => $bulanTotals,
            'totalPendapatan' => $totalPendapatan,
        ]);
    }

    public function requestPembayaran(Request $request, $id)
    {
        $invoice = Invoice::with('customer', 'paket')->findOrFail($id);

        DB::beginTransaction();

        try {
            $totalTagihan = $invoice->tagihan + $invoice->tambahan;
            $jumlahBayar = $request->jumlah_bayar ?? 0;

            $sisa = $jumlahBayar - $totalTagihan;

            // Upload bukti pembayaran jika ada
            $buktiPath = null;
            if ($request->hasFile('bukti_pembayaran')) {
                $file = $request->file('bukti_pembayaran');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $buktiPath = $file->storeAs('bukti_pendapatan', $fileName, 'public');
            }

            // Simpan pembayaran
            $pembayaran = Pembayaran::create([
                'invoice_id'    => $request->invoice_id,
                'jumlah_bayar'  => $jumlahBayar,
                'tanggal_bayar' => now(),
                'metode_bayar'  => $request->metode_id,
                'keterangan'    => 'Pembayaran oleh admin ' . auth()->user()->name . ' untuk ' . $invoice->customer->nama_customer,
                'status_id'     => 8, // Langsung disetujui
                'user_id'       => auth()->id(),
                'bukti_bayar'   => $buktiPath,
                'saldo'         => $sisa > 0 ? $sisa : 0,
            ]);

            // Kirim notifikasi
            $chat = new ChatServices();
            $chat->pembayaranBerhasil($invoice->customer->no_hp, $pembayaran);

            // Update status invoice lama
            $invoice->update(['status_id' => 8]);

            // Hitung tanggal invoice bulan depan (PASTIKAN TIDAK LOMPAT 2 BULAN)
            $tanggalJatuhTempoLama = Carbon::parse($invoice->jatuh_tempo);
            $tanggalAwal = $tanggalJatuhTempoLama->copy()->addMonthsNoOverflow()->startOfMonth();
            $tanggalJatuhTempo = $tanggalAwal->copy()->endOfMonth();

            // Cek apakah invoice bulan depan sudah ada
            $sudahAda = Invoice::where('customer_id', $invoice->customer_id)
                ->whereMonth('jatuh_tempo', $tanggalJatuhTempo->month)
                ->whereYear('jatuh_tempo', $tanggalJatuhTempo->year)
                ->exists();

            // Buat invoice baru jika belum ada
            if (!$sudahAda) {
                Invoice::create([
                    'customer_id'     => $invoice->customer_id,
                    'paket_id'        => $invoice->paket_id,
                    'tagihan'         => $invoice->paket->harga,
                    'tambahan'        => 0,
                    'saldo'           => $sisa > 0 ? $sisa : 0,
                    'status_id'       => 7, // Belum bayar
                    'created_at'      => $tanggalAwal,
                    'updated_at'      => $tanggalAwal,
                    'jatuh_tempo'     => $tanggalJatuhTempo,
                    'tanggal_blokir'  => $invoice->tanggal_blokir,
                ]);
            }

            // Catat keuangan ke kas
            Kas::create([
                'debit'        => $pembayaran->jumlah_bayar,
                'tanggal_kas'  => $pembayaran->tanggal_bayar,
                'keterangan'   => 'Pembayaran dari ' . $pembayaran->invoice->customer->nama_customer,
                'kas_id'       => 1,
                'user_id'      => auth()->id(),
                'pengeluaran_id' => null,
            ]);

            DB::commit();

            return redirect()->back()->with('success', 'Pembayaran berhasil disimpan dan invoice diperbarui.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Gagal menyimpan pembayaran: ' . $e->getMessage());
        }
    }



    public function agen(Request $request)
    {
        $query = User::whereIn('roles_id', [6, 7])->withCount('customer');

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhere('alamat', 'LIKE', "%{$search}%")
                  ->orWhere('no_hp', 'LIKE', "%{$search}%");
            });
        }

        $agen = $query->paginate(10);

        // If this is an AJAX request, return JSON
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $agen,
                'html' => view('keuangan.partials.agen-table-rows', compact('agen'))->render()
            ]);
        }

        return view('/keuangan/data-agen',[
            'users' => Auth::user(),
            'roles' => Auth::user()->roles,
            'agen' => $agen,
        ]);
    }

    public function searchAgen(Request $request)
    {
        $query = User::where('roles_id', 6)->withCount('customer');

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhere('alamat', 'LIKE', "%{$search}%")
                  ->orWhere('no_hp', 'LIKE', "%{$search}%");
            });
        }

        $agen = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $agen->items(),
            'pagination' => [
                'current_page' => $agen->currentPage(),
                'last_page' => $agen->lastPage(),
                'per_page' => $agen->perPage(),
                'total' => $agen->total(),
            ]
        ]);
    }

    public function pelangganAgen(Request $request, $id)
    {
        // Get agen info
        $agen = User::findOrFail($id);

        // Dapatkan bulan saat ini dalam format angka (01-12)
        $currentMonth = now()->format('m');
        $filterMonth = $request->get('month', $currentMonth); // Default ke bulan sekarang

        // Get all invoices from customers under this agent
        $invoicesQuery = Invoice::with([
            'customer.paket',
            'status',
            'pembayaran.user' // Tambahan
        ])->whereHas('customer.agen', function($query) use ($id) {
            $query->where('agen_id', $id)
                  ->where('status_id', 3);
        });
        
        

        // Filter berdasarkan bulan (format sederhana: 01-12)
        $filterMonth = $request->get('month', now()->format('m')); // Default ke bulan sekarang

        if ($filterMonth !== 'all') {
            // Filter berdasarkan bulan yang dipilih (format: 01, 02, 03, dst)
            $invoicesQuery->whereRaw("MONTH(jatuh_tempo) = ?", [intval($filterMonth)]);
        }
        // Jika month = 'all', tidak ada filter tambahan

        // Filter berdasarkan status tagihan
        $filterStatus = $request->get('status');
        if ($filterStatus) {
            // Gunakan pendekatan yang lebih fleksibel untuk filter status
            if ($filterStatus == 'Sudah Bayar') {
                $invoicesQuery->whereHas('status', function($q) {
                    $q->where('nama_status', 'Sudah Bayar');
                });
            } elseif ($filterStatus == 'Belum Bayar') {
                $invoicesQuery->whereHas('status', function($q) {
                    $q->where('nama_status', 'Belum Bayar');
                });
            }
        }

        $invoices = $invoicesQuery->orderBy('jatuh_tempo', 'desc')->paginate(10);

        // Calculate totals for ALL invoices (not just paginated ones) dengan filter yang sama
        $allInvoicesQuery = Invoice::with(['customer', 'status'])
            ->whereHas('customer', function($q) use ($id) {
                $q->where('agen_id', $id)->where('status_id', 3);
            });

        // Terapkan filter bulan yang sama untuk perhitungan total
        if ($filterMonth !== 'all') {
            $allInvoicesQuery->whereRaw("MONTH(jatuh_tempo) = ?", [intval($filterMonth)]);
        }

        // Terapkan filter status yang sama untuk perhitungan total
        if ($filterStatus) {
            if ($filterStatus == 'Sudah Bayar') {
                $allInvoicesQuery->whereHas('status', function($q) {
                    $q->where('nama_status', 'Sudah Bayar');
                });
            } elseif ($filterStatus == 'Belum Bayar') {
                $allInvoicesQuery->whereHas('status', function($q) {
                    $q->where('nama_status', 'Belum Bayar');
                });
            }
        }

        $allInvoices = $allInvoicesQuery->get();

        // Calculate statistics for all data
        $totalPaid = 0;
        $totalUnpaid = 0;
        $totalAmount = 0;

        foreach ($allInvoices as $invoice) {
            $tagihan = $invoice->tagihan ?? 0;
            $totalAmount += $tagihan;

            if ($invoice->status && $invoice->status->nama_status == 'Sudah Bayar') {
                $totalPaid += $tagihan;
            } else {
                $totalUnpaid += $tagihan;
            }
        }

        if (!$agen) {
            abort(404, 'Agen not found');
        }

        // Data untuk view
        $monthNames = [
            '01' => 'Januari', '02' => 'Februari', '03' => 'Maret', '04' => 'April',
            '05' => 'Mei', '06' => 'Juni', '07' => 'Juli', '08' => 'Agustus',
            '09' => 'September', '10' => 'Oktober', '11' => 'November', '12' => 'Desember'
        ];

        $currentMonthNum = now()->format('m');
        $currentMonthName = $monthNames[$currentMonthNum];

        return view('keuangan.data-pelanggan-agen',[
            'users' => Auth::user(),
            'roles' => Auth::user()->roles,
            'invoices' => $invoices,
            'agen' => $agen,
            'totalPaid' => $totalPaid,
            'totalUnpaid' => $totalUnpaid,
            'totalAmount' => $totalAmount,
            'currentMonth' => $currentMonth,
            'filterMonth' => $filterMonth,
            'filterStatus' => $filterStatus,
            'monthNames' => $monthNames,
            'currentMonthNum' => $currentMonthNum,
            'currentMonthName' => $currentMonthName,
        ]);
    }

    public function laporan(Request $request)
    {
        $tahun = $request->tahun ?? now()->year;

        $laporan = [];
        $totalPendapatanLangganan = 0;
        $totalPendapatanLain = 0;
        $totalPengeluaran = 0;
        $totalLabaRugi = 0;
        $bulanList = [];

        // Calculate previous year data for comparison
        $tahunSebelumnya = $tahun - 1;
        $totalPendapatanTahunSebelumnya = Pembayaran::whereHas('status', function($q) {
            $q->where('nama_status', 'Selesai');
        })->whereYear('tanggal_bayar', $tahunSebelumnya)->sum('jumlah_bayar') +
        Pendapatan::whereYear('tanggal', $tahunSebelumnya)->sum('jumlah_pendapatan');

        $totalPengeluaranTahunSebelumnya = Pengeluaran::whereHas('status', function($q) {
            $q->where('nama_status', 'Selesai');
        })->whereYear('tanggal_pengeluaran', $tahunSebelumnya)->sum('jumlah_pengeluaran');

        for ($i = 1; $i <= 12; $i++) {
            $bulanNama = Carbon::create()->month($i)->translatedFormat('F');
            $bulanList[] = $bulanNama;

            $pendapatanLangganan = Pembayaran::whereYear('created_at', $tahun)
                ->whereMonth('created_at', $i)
                ->sum('jumlah_bayar');

            $pendapatanLain = Pendapatan::whereYear('created_at', $tahun)
                ->whereMonth('created_at', $i)
                ->sum('jumlah_pendapatan');

            $pengeluaran = Pengeluaran::whereYear('created_at', $tahun)
                ->whereMonth('created_at', $i)
                ->sum('jumlah_pengeluaran');

            $pendapatanTotal = $pendapatanLangganan + $pendapatanLain;
            $labaRugi = $pendapatanTotal - $pengeluaran;
            $status = $labaRugi >= 0 ? 'Laba' : 'Rugi';

            $totalPendapatanLangganan += $pendapatanLangganan;
            $totalPendapatanLain += $pendapatanLain;
            $totalPengeluaran += $pengeluaran;
            $totalLabaRugi += $labaRugi;

            $laporan[] = [
                'bulan' => $bulanNama,
                'pendapatan_langganan' => $pendapatanLangganan,
                'pendapatan_nonlangganan' => $pendapatanLain,
                'pendapatan' => $pendapatanTotal, // Add this for chart compatibility
                'pengeluaran' => $pengeluaran,
                'pendapatan_total' => $pendapatanTotal,
                'laba_rugi' => $labaRugi,
                'status' => $status,
            ];
        }

        // Kas besar dan kecil tahunan
        $kasBesar = Kas::where('kas_id', 1)->whereYear('created_at', $tahun)->sum('debit');
        $kasKecil = Kas::where('kas_id', 2)->whereYear('created_at', $tahun)->sum('debit');

        // RAB
        $rabData = Rab::whereYear('created_at', $tahun)->get()->map(function ($rab) {
            $pengeluaran = Pengeluaran::where('rab_id', $rab->id)->sum('jumlah_pengeluaran');
            return [
                'nama' => $rab->keterangan,
                'anggaran' => $rab->jumlah_anggaran,
                'realisasi' => $pengeluaran,
                'sisa' => $rab->jumlah_anggaran - $pengeluaran,
            ];
        });

        // Calculate growth rates
        $pendapatanGrowth = $totalPendapatanTahunSebelumnya > 0 ?
            (($totalPendapatanLangganan + $totalPendapatanLain - $totalPendapatanTahunSebelumnya) / $totalPendapatanTahunSebelumnya) * 100 : 0;

        $pengeluaranGrowth = $totalPengeluaranTahunSebelumnya > 0 ?
            (($totalPengeluaran - $totalPengeluaranTahunSebelumnya) / $totalPengeluaranTahunSebelumnya) * 100 : 0;

        $profitMargin = ($totalPendapatanLangganan + $totalPendapatanLain) > 0 ?
            ($totalLabaRugi / ($totalPendapatanLangganan + $totalPendapatanLain)) * 100 : 0;

        $efficiencyRatio = ($totalPendapatanLangganan + $totalPendapatanLain) > 0 ?
            ($totalPengeluaran / ($totalPendapatanLangganan + $totalPendapatanLain)) * 100 : 0;

        return view('keuangan.laporan.laporan', [
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'tahun' => $tahun,
            'laporan' => $laporan,
            'totalPendapatanLangganan' => $totalPendapatanLangganan,
            'totalPendapatanLain' => $totalPendapatanLain,
            'totalPendapatan' => $totalPendapatanLangganan + $totalPendapatanLain,
            'totalPengeluaran' => $totalPengeluaran,
            'totalLabaRugi' => $totalLabaRugi,
            'kasBesar' => $kasBesar,
            'kasKecil' => $kasKecil,
            'rabData' => $rabData,
            'bulanList' => $bulanList,
            'tahunSebelumnya' => $totalPendapatanTahunSebelumnya,
            'pengeluaranTahunSebelumnya' => $totalPengeluaranTahunSebelumnya,
            'pendapatanGrowth' => $pendapatanGrowth,
            'pengeluaranGrowth' => $pengeluaranGrowth,
            'profitMargin' => $profitMargin,
            'efficiencyRatio' => $efficiencyRatio,
            'totalTransaksi' => Pembayaran::whereYear('tanggal_bayar', $tahun)->count() +
                               Pengeluaran::whereYear('tanggal_pengeluaran', $tahun)->count(),
        ]);
    }

    public function filterLaporan(Request $request)
    {
        try {
            $filters = $request->all();

            // Get filtered data based on filters
            $filteredData = $this->getFilteredFinancialData($filters);

            return response()->json([
                'success' => true,
                'data' => $filteredData,
                'kpi' => $this->calculateKPI($filteredData),
                'chartData' => $this->generateChartData($filteredData, $filters),
                'tableData' => $this->generateTableData($filteredData),
                'totalRecords' => $filteredData['totalRecords'] ?? 0
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error applying filters: ' . $e->getMessage()
            ], 500);
        }
    }

    public function realtimeData()
    {
        try {
            // Get real-time metrics
            $metrics = [
                'totalPendapatan' => Pembayaran::whereHas('status', function($q) {
                    $q->where('nama_status', 'Selesai');
                })->sum('jumlah_bayar') + Pendapatan::sum('jumlah_pendapatan'),

                'totalPengeluaran' => Pengeluaran::whereHas('status', function($q) {
                    $q->where('nama_status', 'Selesai');
                })->sum('jumlah_pengeluaran'),

                'activeTransactions' => Pembayaran::whereDate('created_at', today())->count(),
                'systemStatus' => 'online'
            ];

            return response()->json([
                'success' => true,
                'metrics' => $metrics,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            \Log::error('Real-time data error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error fetching real-time data'
            ], 500);
        }
    }

    public function exportLaporan(Request $request)
    {
        try {
            $filters = $request->all();
            $data = $this->getFilteredFinancialData($filters);

            // Generate Excel export
            $filename = 'Laporan_Keuangan_' . ($filters['tahun'] ?? date('Y')) . '_' . date('YmdHis') . '.xlsx';

            return $this->generateExcelExport($data, $filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error exporting data: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getFilteredFinancialData($filters)
    {
        $tahun = $filters['tahun'] ?? date('Y');
        $bulanRange = $filters['bulan_range'] ?? 'all';
        $startDate = $filters['start_date'] ?? null;
        $endDate = $filters['end_date'] ?? null;
        $minAmount = $filters['min_amount'] ?? null;
        $maxAmount = $filters['max_amount'] ?? null;

        // Build base queries
        $pendapatanQuery = Pembayaran::whereHas('status', function($q) {
            $q->where('nama_status', 'Selesai');
        })->whereYear('tanggal_bayar', $tahun);

        $pengeluaranQuery = Pengeluaran::whereHas('status', function($q) {
            $q->where('nama_status', 'Selesai');
        })->whereYear('tanggal_pengeluaran', $tahun);

        // Apply date range filters
        if ($startDate && $endDate) {
            $pendapatanQuery->whereBetween('tanggal_bayar', [$startDate, $endDate]);
            $pengeluaranQuery->whereBetween('tanggal_pengeluaran', [$startDate, $endDate]);
        } elseif ($bulanRange !== 'all') {
            $monthRanges = $this->getMonthRanges($bulanRange);
            if ($monthRanges) {
                $pendapatanQuery->whereIn(DB::raw('MONTH(tanggal_bayar)'), $monthRanges);
                $pengeluaranQuery->whereIn(DB::raw('MONTH(tanggal_pengeluaran)'), $monthRanges);
            }
        }

        // Apply amount filters
        if ($minAmount) {
            $pendapatanQuery->where('jumlah_bayar', '>=', $minAmount);
            $pengeluaranQuery->where('jumlah_pengeluaran', '>=', $minAmount);
        }

        if ($maxAmount) {
            $pendapatanQuery->where('jumlah_bayar', '<=', $maxAmount);
            $pengeluaranQuery->where('jumlah_pengeluaran', '<=', $maxAmount);
        }

        // Get aggregated data
        $totalPendapatan = $pendapatanQuery->sum('jumlah_bayar') +
                          Pendapatan::whereYear('tanggal', $tahun)->sum('jumlah_pendapatan');
        $totalPengeluaran = $pengeluaranQuery->sum('jumlah_pengeluaran');
        $totalLabaRugi = $totalPendapatan - $totalPengeluaran;

        // Get monthly breakdown
        $monthlyData = $this->getMonthlyBreakdown($tahun, $filters);

        return [
            'totalPendapatan' => $totalPendapatan,
            'totalPengeluaran' => $totalPengeluaran,
            'totalLabaRugi' => $totalLabaRugi,
            'monthlyData' => $monthlyData,
            'totalRecords' => $pendapatanQuery->count() + $pengeluaranQuery->count(),
            'filters' => $filters
        ];
    }

    private function getMonthRanges($range)
    {
        switch ($range) {
            case 'q1': return [1, 2, 3];
            case 'q2': return [4, 5, 6];
            case 'q3': return [7, 8, 9];
            case 'q4': return [10, 11, 12];
            case 'h1': return [1, 2, 3, 4, 5, 6];
            case 'h2': return [7, 8, 9, 10, 11, 12];
            default: return null;
        }
    }

    private function getMonthlyBreakdown($tahun, $filters = [])
    {
        $monthlyData = [];
        $monthNames = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        for ($month = 1; $month <= 12; $month++) {
            $pendapatan = Pembayaran::whereHas('status', function($q) {
                $q->where('nama_status', 'Selesai');
            })->whereYear('tanggal_bayar', $tahun)
              ->whereMonth('tanggal_bayar', $month)
              ->sum('jumlah_bayar') +
              Pendapatan::whereYear('tanggal', $tahun)
              ->whereMonth('tanggal', $month)
              ->sum('jumlah_pendapatan');

            $pengeluaran = Pengeluaran::whereHas('status', function($q) {
                $q->where('nama_status', 'Selesai');
            })->whereYear('tanggal_pengeluaran', $tahun)
              ->whereMonth('tanggal_pengeluaran', $month)
              ->sum('jumlah_pengeluaran');

            $labaRugi = $pendapatan - $pengeluaran;

            $monthlyData[] = [
                'bulan' => $monthNames[$month],
                'month_number' => $month,
                'pendapatan' => $pendapatan,
                'pengeluaran' => $pengeluaran,
                'laba_rugi' => $labaRugi,
                'margin' => $pendapatan > 0 ? ($labaRugi / $pendapatan) * 100 : 0
            ];
        }

        return $monthlyData;
    }

    private function calculateKPI($data)
    {
        $profitMargin = $data['totalPendapatan'] > 0 ?
                       ($data['totalLabaRugi'] / $data['totalPendapatan']) * 100 : 0;

        $efficiencyRatio = $data['totalPendapatan'] > 0 ?
                          ($data['totalPengeluaran'] / $data['totalPendapatan']) * 100 : 0;

        $roi = $data['totalPengeluaran'] > 0 ?
               ($data['totalLabaRugi'] / $data['totalPengeluaran']) * 100 : 0;

        return [
            'profitMargin' => round($profitMargin, 2),
            'efficiencyRatio' => round($efficiencyRatio, 2),
            'roi' => round($roi, 2),
            'growthRate' => 0 // Calculate based on historical data
        ];
    }

    private function generateChartData($data, $filters)
    {
        $chartType = $filters['chart_type'] ?? 'line';
        $monthlyData = $data['monthlyData'];

        $labels = array_column($monthlyData, 'bulan');
        $pendapatanData = array_column($monthlyData, 'pendapatan');
        $pengeluaranData = array_column($monthlyData, 'pengeluaran');
        $profitData = array_column($monthlyData, 'laba_rugi');

        $datasets = [];

        if ($chartType === 'combo' || $chartType === 'line' || $chartType === 'bar') {
            $datasets[] = [
                'label' => 'Pendapatan',
                'data' => $pendapatanData,
                'borderColor' => '#28a745',
                'backgroundColor' => 'rgba(40, 167, 69, 0.1)',
                'type' => $chartType === 'combo' ? 'line' : $chartType
            ];

            $datasets[] = [
                'label' => 'Pengeluaran',
                'data' => $pengeluaranData,
                'borderColor' => '#dc3545',
                'backgroundColor' => 'rgba(220, 53, 69, 0.1)',
                'type' => $chartType === 'combo' ? 'bar' : $chartType
            ];
        }

        return [
            'labels' => $labels,
            'datasets' => $datasets,
            'type' => $chartType
        ];
    }

    private function generateTableData($data)
    {
        $monthlyData = $data['monthlyData'];
        $rows = '';

        foreach ($monthlyData as $item) {
            $margin = $item['margin'];
            $statusClass = $margin >= 15 ? 'success' : ($margin >= 10 ? 'warning' : 'danger');
            $statusText = $margin >= 15 ? 'Excellent' : ($margin >= 10 ? 'Good' : 'Poor');

            $rows .= '<tr>';
            $rows .= '<td class="fw-bold">' . $item['bulan'] . '</td>';
            $rows .= '<td class="text-end text-success">Rp ' . number_format($item['pendapatan'], 0, ',', '.') . '</td>';
            $rows .= '<td class="text-end text-danger">Rp ' . number_format($item['pengeluaran'], 0, ',', '.') . '</td>';
            $rows .= '<td class="text-end text-' . ($item['laba_rugi'] >= 0 ? 'success' : 'danger') . '">Rp ' . number_format($item['laba_rugi'], 0, ',', '.') . '</td>';
            $rows .= '<td class="text-center"><span class="badge bg-' . $statusClass . '">' . number_format($margin, 1) . '%</span></td>';
            $rows .= '<td class="text-center"><span class="badge bg-' . $statusClass . '">' . $statusText . '</span></td>';
            $rows .= '</tr>';
        }

        return ['rows' => $rows];
    }

    private function generateExcelExport($data, $filename)
    {
        // This would typically use a library like PhpSpreadsheet
        // For now, return a simple CSV response

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Add headers
            fputcsv($file, ['Bulan', 'Pendapatan', 'Pengeluaran', 'Laba/Rugi', 'Margin (%)']);

            // Add data
            foreach ($data['monthlyData'] as $row) {
                fputcsv($file, [
                    $row['bulan'],
                    $row['pendapatan'],
                    $row['pengeluaran'],
                    $row['laba_rugi'],
                    number_format($row['margin'], 2)
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function predictiveAnalysis(Request $request)
    {
        try {
            $tahun = $request->get('tahun', date('Y'));
            $months = $request->get('months', 6); // Predict next 6 months

            // Get historical data for prediction
            $historicalData = $this->getHistoricalDataForPrediction($tahun);

            // Generate predictions
            $predictions = [
                'revenue' => $this->predictRevenue($historicalData, $months),
                'expenses' => $this->predictExpenses($historicalData, $months),
                'cashFlow' => $this->predictCashFlow($historicalData, $months),
                'trends' => $this->analyzeTrends($historicalData),
                'recommendations' => $this->generateRecommendations($historicalData)
            ];

            return response()->json([
                'success' => true,
                'predictions' => $predictions,
                'confidence' => $this->calculateConfidenceLevel($historicalData),
                'methodology' => 'Linear Regression with Seasonal Adjustment'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating predictions: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getHistoricalDataForPrediction($tahun)
    {
        $data = [];

        // Get data for current year and previous 2 years
        for ($year = $tahun - 2; $year <= $tahun; $year++) {
            for ($month = 1; $month <= 12; $month++) {
                // Skip future months for current year
                if ($year == $tahun && $month > date('n')) {
                    continue;
                }

                $revenue = Pembayaran::whereHas('status', function($q) {
                    $q->where('nama_status', 'Selesai');
                })->whereYear('tanggal_bayar', $year)
                  ->whereMonth('tanggal_bayar', $month)
                  ->sum('jumlah_bayar') +
                  Pendapatan::whereYear('tanggal', $year)
                  ->whereMonth('tanggal', $month)
                  ->sum('jumlah_pendapatan');

                $expenses = Pengeluaran::whereHas('status', function($q) {
                    $q->where('nama_status', 'Selesai');
                })->whereYear('tanggal_pengeluaran', $year)
                  ->whereMonth('tanggal_pengeluaran', $month)
                  ->sum('jumlah_pengeluaran');

                $data[] = [
                    'year' => $year,
                    'month' => $month,
                    'revenue' => $revenue,
                    'expenses' => $expenses,
                    'profit' => $revenue - $expenses,
                    'date' => Carbon::create($year, $month, 1)
                ];
            }
        }

        return $data;
    }

    private function predictRevenue($historicalData, $months)
    {
        $revenues = array_column($historicalData, 'revenue');
        $predictions = [];

        // Simple linear regression with seasonal adjustment
        $trend = $this->calculateTrend($revenues);
        $seasonality = $this->calculateSeasonality($historicalData);

        $lastMonth = date('n');
        $lastYear = date('Y');

        for ($i = 1; $i <= $months; $i++) {
            $futureMonth = ($lastMonth + $i - 1) % 12 + 1;
            $futureYear = $lastYear + floor(($lastMonth + $i - 1) / 12);

            // Base prediction using trend
            $basePrediction = end($revenues) + ($trend * $i);

            // Apply seasonal adjustment
            $seasonalFactor = $seasonality[$futureMonth] ?? 1;
            $prediction = $basePrediction * $seasonalFactor;

            // Add some randomness for realism (±5%)
            $variance = $prediction * 0.05 * (mt_rand(-100, 100) / 100);
            $prediction += $variance;

            $predictions[] = [
                'month' => $futureMonth,
                'year' => $futureYear,
                'predicted_revenue' => max(0, round($prediction)),
                'confidence' => max(0.6, 0.9 - ($i * 0.05)), // Decreasing confidence
                'trend_component' => round($basePrediction),
                'seasonal_component' => round($prediction - $basePrediction)
            ];
        }

        return $predictions;
    }

    private function predictExpenses($historicalData, $months)
    {
        $expenses = array_column($historicalData, 'expenses');
        $predictions = [];

        $trend = $this->calculateTrend($expenses);
        $seasonality = $this->calculateSeasonality($historicalData, 'expenses');

        $lastMonth = date('n');
        $lastYear = date('Y');

        for ($i = 1; $i <= $months; $i++) {
            $futureMonth = ($lastMonth + $i - 1) % 12 + 1;
            $futureYear = $lastYear + floor(($lastMonth + $i - 1) / 12);

            $basePrediction = end($expenses) + ($trend * $i);
            $seasonalFactor = $seasonality[$futureMonth] ?? 1;
            $prediction = $basePrediction * $seasonalFactor;

            // Expenses tend to be more stable, less variance
            $variance = $prediction * 0.03 * (mt_rand(-100, 100) / 100);
            $prediction += $variance;

            $predictions[] = [
                'month' => $futureMonth,
                'year' => $futureYear,
                'predicted_expenses' => max(0, round($prediction)),
                'confidence' => max(0.7, 0.95 - ($i * 0.04)),
                'trend_component' => round($basePrediction),
                'seasonal_component' => round($prediction - $basePrediction)
            ];
        }

        return $predictions;
    }

    private function predictCashFlow($historicalData, $months)
    {
        $cashFlows = array_map(function($item) {
            return $item['revenue'] - $item['expenses'];
        }, $historicalData);

        $predictions = [];
        $trend = $this->calculateTrend($cashFlows);

        $lastMonth = date('n');
        $lastYear = date('Y');

        for ($i = 1; $i <= $months; $i++) {
            $futureMonth = ($lastMonth + $i - 1) % 12 + 1;
            $futureYear = $lastYear + floor(($lastMonth + $i - 1) / 12);

            $prediction = end($cashFlows) + ($trend * $i);

            // Cash flow can be more volatile
            $variance = abs($prediction) * 0.1 * (mt_rand(-100, 100) / 100);
            $prediction += $variance;

            $predictions[] = [
                'month' => $futureMonth,
                'year' => $futureYear,
                'predicted_cashflow' => round($prediction),
                'confidence' => max(0.5, 0.85 - ($i * 0.06)),
                'status' => $prediction > 0 ? 'positive' : 'negative'
            ];
        }

        return $predictions;
    }

    private function calculateTrend($data)
    {
        $n = count($data);
        if ($n < 2) return 0;

        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumX2 = 0;

        for ($i = 0; $i < $n; $i++) {
            $x = $i + 1;
            $y = $data[$i];

            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }

        // Linear regression slope
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);

        return $slope;
    }

    private function calculateSeasonality($historicalData, $field = 'revenue')
    {
        $monthlyAverages = [];
        $monthlyData = [];

        // Group data by month
        foreach ($historicalData as $item) {
            $month = $item['month'];
            if (!isset($monthlyData[$month])) {
                $monthlyData[$month] = [];
            }
            $monthlyData[$month][] = $item[$field];
        }

        // Calculate average for each month
        $overallAverage = array_sum(array_column($historicalData, $field)) / count($historicalData);

        for ($month = 1; $month <= 12; $month++) {
            if (isset($monthlyData[$month]) && count($monthlyData[$month]) > 0) {
                $monthAverage = array_sum($monthlyData[$month]) / count($monthlyData[$month]);
                $monthlyAverages[$month] = $overallAverage > 0 ? $monthAverage / $overallAverage : 1;
            } else {
                $monthlyAverages[$month] = 1; // No seasonal effect
            }
        }

        return $monthlyAverages;
    }

    private function analyzeTrends($historicalData)
    {
        $revenueData = array_column($historicalData, 'revenue');
        $expenseData = array_column($historicalData, 'expenses');
        $profitData = array_column($historicalData, 'profit');

        return [
            'revenue_trend' => $this->getTrendDirection($revenueData),
            'expense_trend' => $this->getTrendDirection($expenseData),
            'profit_trend' => $this->getTrendDirection($profitData),
            'volatility' => [
                'revenue' => $this->calculateVolatility($revenueData),
                'expenses' => $this->calculateVolatility($expenseData),
                'profit' => $this->calculateVolatility($profitData)
            ],
            'growth_rate' => [
                'revenue' => $this->calculateGrowthRate($revenueData),
                'expenses' => $this->calculateGrowthRate($expenseData)
            ]
        ];
    }

    private function getTrendDirection($data)
    {
        $trend = $this->calculateTrend($data);

        if ($trend > 0) {
            return ['direction' => 'increasing', 'strength' => min(abs($trend) / 1000000, 1)];
        } elseif ($trend < 0) {
            return ['direction' => 'decreasing', 'strength' => min(abs($trend) / 1000000, 1)];
        } else {
            return ['direction' => 'stable', 'strength' => 0];
        }
    }

    private function calculateVolatility($data)
    {
        $n = count($data);
        if ($n < 2) return 0;

        $mean = array_sum($data) / $n;
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $data)) / $n;

        $stdDev = sqrt($variance);

        // Return coefficient of variation (relative volatility)
        return $mean > 0 ? $stdDev / $mean : 0;
    }

    private function calculateGrowthRate($data)
    {
        $n = count($data);
        if ($n < 2) return 0;

        $firstValue = $data[0];
        $lastValue = $data[$n - 1];
        $periods = $n - 1;

        if ($firstValue <= 0 || $periods <= 0) return 0;

        // Compound annual growth rate
        $growthRate = pow($lastValue / $firstValue, 1 / $periods) - 1;

        return $growthRate * 100; // Return as percentage
    }

    private function generateRecommendations($historicalData)
    {
        $trends = $this->analyzeTrends($historicalData);
        $recommendations = [];

        // Revenue recommendations
        if ($trends['revenue_trend']['direction'] === 'decreasing') {
            $recommendations[] = [
                'type' => 'revenue',
                'priority' => 'high',
                'title' => 'Revenue Decline Alert',
                'description' => 'Revenue shows declining trend. Consider marketing initiatives or service improvements.',
                'action_items' => [
                    'Review pricing strategy',
                    'Analyze customer satisfaction',
                    'Implement retention programs',
                    'Explore new revenue streams'
                ]
            ];
        }

        // Expense recommendations
        if ($trends['expense_trend']['direction'] === 'increasing' &&
            $trends['expense_trend']['strength'] > 0.5) {
            $recommendations[] = [
                'type' => 'expenses',
                'priority' => 'medium',
                'title' => 'Rising Expenses',
                'description' => 'Expenses are increasing faster than revenue. Cost optimization needed.',
                'action_items' => [
                    'Audit operational costs',
                    'Negotiate supplier contracts',
                    'Implement cost control measures',
                    'Review staffing efficiency'
                ]
            ];
        }

        // Volatility recommendations
        if ($trends['volatility']['revenue'] > 0.3) {
            $recommendations[] = [
                'type' => 'stability',
                'priority' => 'medium',
                'title' => 'High Revenue Volatility',
                'description' => 'Revenue shows high volatility. Consider stabilization strategies.',
                'action_items' => [
                    'Diversify revenue sources',
                    'Implement subscription models',
                    'Build customer loyalty programs',
                    'Create predictable revenue streams'
                ]
            ];
        }

        // Growth recommendations
        if ($trends['growth_rate']['revenue'] < 5) {
            $recommendations[] = [
                'type' => 'growth',
                'priority' => 'low',
                'title' => 'Low Growth Rate',
                'description' => 'Revenue growth is below industry standards. Consider expansion strategies.',
                'action_items' => [
                    'Market expansion analysis',
                    'Product development initiatives',
                    'Strategic partnerships',
                    'Digital transformation'
                ]
            ];
        }

        return $recommendations;
    }

    private function calculateConfidenceLevel($historicalData)
    {
        $dataPoints = count($historicalData);
        $dataQuality = min($dataPoints / 36, 1); // 3 years of data = 100% quality

        $revenueData = array_column($historicalData, 'revenue');
        $volatility = $this->calculateVolatility($revenueData);
        $stabilityScore = max(0, 1 - $volatility);

        $overallConfidence = ($dataQuality * 0.6) + ($stabilityScore * 0.4);

        return [
            'overall' => round($overallConfidence * 100, 1),
            'data_quality' => round($dataQuality * 100, 1),
            'stability_score' => round($stabilityScore * 100, 1),
            'recommendation' => $overallConfidence > 0.8 ? 'High confidence' :
                              ($overallConfidence > 0.6 ? 'Medium confidence' : 'Low confidence')
        ];
    }
}
